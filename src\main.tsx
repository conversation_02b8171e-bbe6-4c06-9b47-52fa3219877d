import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap-icons/font/bootstrap-icons.css';
import 'bootstrap/dist/js/bootstrap.bundle.min.js';
import App from './App';
import Home from './pages/Home';
import Login from './pages/Login';
import CreatorAccount from './pages/CreatorAccount';
import About from './pages/About';
import PrivacyPolicy from './pages/PrivacyPolicy';
import RefundPolicy from './pages/RefundPolicy';
import ABACPolicy from './pages/ABACPolicy';
import CookiesPolicy from './pages/CookiesPolicy';
import ScamAlert from './pages/ScamAlert';
import Test from './pages/Test';
import ContactUs from './pages/ContactUs/ContactUs';
import ArbitrageScreener from './pages/screener/screener';
import PnLSimulator
 from './pages/Pnl/Pnl';
createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <BrowserRouter>
      <Routes>
        <Route path="/" element={<App />}>
          <Route index element={<Home />} />
          <Route path="login" element={<Login />} />
          <Route path="creator-account" element={<CreatorAccount />} />
          <Route path="about" element={<About />} />
          <Route path="privacy-policy" element={<PrivacyPolicy />} />
          <Route path="refund-policy" element={<RefundPolicy />} />
          <Route path="abac-policy" element={<ABACPolicy />} />
          <Route path="cookies-policy" element={<CookiesPolicy />} />
          <Route path="scam-alert" element={<ScamAlert />} />
          <Route path="test" element={<Test />} />
           <Route path="Contact-Us" element={<ContactUs />} />
        <Route path="Arbitrage-Screener" element={<ArbitrageScreener />} />
        <Route path="pnl-simulator/:symbol" element={<PnLSimulator/>} />
        </Route>
      </Routes>
    </BrowserRouter>
  </StrictMode>,
);
