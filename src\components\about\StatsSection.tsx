import React from 'react';

interface Stat {
  number: string;
  label: string;
}

const StatsSection: React.FC = () => {
  const stats: Stat[] = [
    {
      number: '212',
      label: 'Projects Completed'
    },
    {
      number: '100k',
      label: 'Active Users'
    },
    {
      number: '89k',
      label: 'Downloads'
    },
    {
      number: '412k',
      label: 'Lines of Code'
    }
  ];

  return (
    <section className="simple-stats-section">
      <div className="container">
        <div className="row">
          {stats.map((stat, index) => (
            <div key={index} className="col-lg-3 col-md-6 mb-4">
              <div className="simple-stat-card">
                <div className="simple-stat-number">{stat.number}</div>
                <h3 className="simple-stat-label">{stat.label}</h3>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default StatsSection;
