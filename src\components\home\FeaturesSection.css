/* Home Features Section - Unique styling to avoid conflicts */
.home-features-section {
  padding: 5rem 0;
  background-color: #FFFBF7;
}

.home-features-section .home-features-container {
  max-width: 1140px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.home-features-section .home-features-header {
  text-align: center;
  margin-bottom: 3.5rem;
}

.home-features-section .home-features-title {
  font-size: 2.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.home-features-section .home-features-subtitle {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* New flexbox-based layout */
.home-features-section .home-features-grid {
  max-width: 1080px;
  margin: 0 auto;
  padding: 0 15px;
}

.home-features-section .home-features-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.home-features-section .home-features-row:last-child {
  margin-bottom: 0;
}

.home-features-section .home-feature-col {
  flex: 0 0 calc(33.333% - 1.25rem);
  max-width: calc(33.333% - 1.25rem);
}

.home-features-section .home-feature-card {
  background-color: #fff !important;
  border-radius: 10px;
  padding: 2.25rem 1.75rem;
  text-align: center;
  border: 1px solid rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 220px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02) !important;
  width: 100%;
}

.home-features-section .home-feature-card:hover {
  transform: translateY(-3px) !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05) !important;
  border-color: rgba(0, 0, 0, 0.3) !important;
}

.home-features-section .home-feature-icon {
  margin-bottom: 1.5rem;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.home-features-section .home-feature-icon img {
  width: 28px;
  height: 28px;
  filter: invert(56%) sepia(75%) saturate(1582%) hue-rotate(346deg) brightness(101%) contrast(101%);
}

.home-features-section .home-feature-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333 !important;
  margin-bottom: 0.85rem;
}

.home-features-section .home-feature-description {
  font-size: 0.9rem;
  color: #666 !important;
  line-height: 1.5;
  max-width: 260px;
  margin: 0 auto;
}

@media (max-width: 992px) {
  .home-features-section .home-features-title {
    font-size: 2rem;
  }

  .home-features-section .home-feature-col {
    flex: 0 0 calc(33.333% - 1rem);
    max-width: calc(33.333% - 1rem);
  }

  .home-features-section .home-features-row {
    margin-bottom: 2rem;
  }

  .home-features-section .home-feature-card {
    padding: 1.75rem 1.25rem;
  }
}

@media (max-width: 768px) {
  .home-features-section {
    padding: 4rem 0;
  }

  .home-features-section .home-features-header {
    margin-bottom: 2.5rem;
  }

  .home-features-section .home-features-title {
    font-size: 1.75rem;
  }

  .home-features-section .home-features-subtitle {
    font-size: 0.95rem;
  }

  .home-features-section .home-features-grid {
    padding: 0 10px;
  }

  .home-features-section .home-features-row {
    margin-bottom: 1.5rem;
  }

  .home-features-section .home-feature-col {
    flex: 0 0 calc(33.333% - 0.75rem);
    max-width: calc(33.333% - 0.75rem);
  }

  .home-features-section .home-feature-icon img {
    width: 24px;
    height: 24px;
  }

  .home-features-section .home-feature-description {
    font-size: 0.85rem;
    max-width: 220px;
  }

  .home-features-section .home-feature-card {
    padding: 1.75rem 1.25rem;
    min-height: 200px;
  }
}

@media (max-width: 576px) {
  .home-features-section {
    padding: 3rem 0;
  }

  .home-features-section .home-features-title {
    font-size: 1.5rem;
  }

  .home-features-section .home-features-subtitle br {
    display: none;
  }

  .home-features-section .home-features-grid {
    overflow-x: auto;
    padding: 0;
  }

  .home-features-section .home-features-row {
    min-width: 480px;
    margin-bottom: 1rem;
    padding: 0 1rem;
  }

  .home-features-section .home-feature-col {
    flex: 0 0 calc(33.333% - 0.5rem);
    max-width: calc(33.333% - 0.5rem);
  }

  .home-features-section .home-feature-card {
    min-height: 170px;
    padding: 1.25rem 0.75rem;
    border: 1px solid rgba(0, 0, 0, 0.2) !important;
  }

  .home-features-section .home-feature-description {
    font-size: 0.75rem;
    max-width: 100%;
  }

  .home-features-section .home-feature-title {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    color: #333 !important;
  }

  .home-features-section .home-feature-icon {
    margin-bottom: 0.75rem;
  }

  .home-features-section .home-feature-icon img {
    width: 20px;
    height: 20px;
  }
}

/* Container for horizontal scrolling on very small screens */
@media (max-width: 480px) {
  .home-features-section .home-features-container {
    padding: 0;
  }

  .home-features-section .home-features-grid {
    min-width: 450px;
  }

  .home-features-section .home-feature-card {
    min-height: 160px;
    border: 1px solid rgba(0, 0, 0, 0.2) !important;
  }
}
