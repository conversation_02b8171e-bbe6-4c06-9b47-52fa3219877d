import { useState } from 'react';
import type { FormField } from '../types';

interface FormConfig {
  initialValues: { [key: string]: FormField };
  onSubmit: (values: { [key: string]: string }) => Promise<void>;
  validate?: (values: { [key: string]: FormField }) => { [key: string]: string };
}

interface FormState {
  values: { [key: string]: string };
  errors: { [key: string]: string };
  touched: { [key: string]: boolean };
}

const useForm = (config: FormConfig) => {
  const { initialValues, onSubmit, validate } = config;

  const [formFields, setFormFields] = useState<{ [key: string]: FormField }>(initialValues);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [formError, setFormError] = useState('');

  const getFormState = (): FormState => {
    const values: { [key: string]: string } = {};
    const errors: { [key: string]: string } = {};
    const touched: { [key: string]: boolean } = {};

    Object.keys(formFields).forEach(key => {
      values[key] = formFields[key].value;
      errors[key] = formFields[key].error || '';
      touched[key] = !!formFields[key].touched;
    });

    return { values, errors, touched };
  };

  const handleChange = (id: string, value: string) => {
    setFormFields(prev => ({
      ...prev,
      [id]: {
        ...prev[id],
        value,
        touched: true,
        error: ''
      }
    }));
  };

  const handleBlur = (id: string) => {
    setFormFields(prev => ({
      ...prev,
      [id]: {
        ...prev[id],
        touched: true
      }
    }));

    if (validate) {
      validateField(id);
    }
  };

  const validateField = (id: string) => {
    // Check required fields
    if (formFields[id].required && !formFields[id].value.trim()) {
      setFormFields(prev => ({
        ...prev,
        [id]: {
          ...prev[id],
          error: `${formFields[id].label} is required`
        }
      }));
      return false;
    }

    // Check email format
    if (id === 'email' && formFields[id].value && !/\S+@\S+\.\S+/.test(formFields[id].value)) {
      setFormFields(prev => ({
        ...prev,
        [id]: {
          ...prev[id],
          error: 'Please enter a valid email address'
        }
      }));
      return false;
    }

    // Custom validation if provided
    if (validate) {
      const customErrors = validate(formFields);
      if (customErrors[id]) {
        setFormFields(prev => ({
          ...prev,
          [id]: {
            ...prev[id],
            error: customErrors[id]
          }
        }));
        return false;
      }
    }

    return true;
  };

  const validateForm = (): boolean => {
    let isValid = true;
    const updatedFields = { ...formFields };

    // Validate all fields
    Object.keys(formFields).forEach(key => {
      const field = formFields[key];

      // Required field validation
      if (field.required && !field.value.trim()) {
        updatedFields[key] = {
          ...field,
          error: `${field.label} is required`,
          touched: true
        };
        isValid = false;
      }

      // Email format validation
      if (key === 'email' && field.value && !/\S+@\S+\.\S+/.test(field.value)) {
        updatedFields[key] = {
          ...field,
          error: 'Please enter a valid email address',
          touched: true
        };
        isValid = false;
      }
    });

    // Custom validation if provided
    if (validate) {
      const customErrors = validate(updatedFields);

      Object.keys(customErrors).forEach(key => {
        if (customErrors[key]) {
          updatedFields[key] = {
            ...updatedFields[key],
            error: customErrors[key],
            touched: true
          };
          isValid = false;
        }
      });
    }

    setFormFields(updatedFields);
    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setFormError('');

    try {
      const values: { [key: string]: string } = {};
      Object.keys(formFields).forEach(key => {
        values[key] = formFields[key].value;
      });

      await onSubmit(values);

      // Reset form and show success message
      setIsSubmitted(true);
    } catch (error) {
      setFormError('An error occurred. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormFields(initialValues);
    setIsSubmitted(false);
    setFormError('');
  };

  return {
    formFields,
    handleChange,
    handleBlur,
    handleSubmit,
    resetForm,
    isSubmitting,
    isSubmitted,
    formError,
    setFormError,
    getFormState
  };
};

export default useForm;
