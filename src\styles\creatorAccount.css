.creator-account-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 76px);
  padding: 2rem 1rem;
  background-color: #FFFBF7;
}

.creator-account-form {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 2.5rem;
  width: 100%;
  max-width: 600px;
  border: 1px solid #e0e0e0;
}

.creator-account-form h1 {
  font-size: 1.75rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.creator-account-form p {
  color: #666;
  margin-bottom: 1.5rem;
}

.form-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 0.5rem;
}

.form-control, .form-select {
  padding: 0.75rem 1rem;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  font-size: 0.9rem;
}

.form-control:focus, .form-select:focus {
  border-color: #FF7A45;
  box-shadow: 0 0 0 0.2rem rgba(255, 122, 69, 0.25);
}

.input-group-text {
  background-color: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-left: none;
  cursor: pointer;
}

.password-toggle {
  cursor: pointer;
  background: none;
  border: 1px solid #e0e0e0;
  border-left: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle:hover {
  background-color: #f0f0f0;
}

.form-text {
  font-size: 0.75rem;
  color: #888;
  margin-top: 0.25rem;
}

.create-account-btn {
  background-color: #FF7A45;
  border-color: #FF7A45;
  padding: 0.75rem;
  font-weight: 500;
  border-radius: 4px;
  color: white;
  font-size: 1rem;
  margin-top: 1rem;
}

.create-account-btn:hover, .create-account-btn:focus {
  background-color: #e86a38;
  border-color: #e86a38;
  color: white;
}

.account-link {
  color: #FF7A45;
  text-decoration: none;
  font-size: 0.9rem;
  display: inline-block;
  margin-top: 1rem;
}

.account-link:hover {
  text-decoration: underline;
}

/* Spacing between form groups */
.mb-3 {
  margin-bottom: 1.25rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

/* Responsive styles */
@media (max-width: 768px) {
  .creator-account-form {
    padding: 1.5rem;
  }

  .row {
    flex-direction: column;
  }

  .col-md-6 {
    width: 100%;
    margin-bottom: 1rem;
  }

  .col-md-6:last-child {
    margin-bottom: 0;
  }
}
