import React from 'react';

const PartnersSection: React.FC = () => {
  const partners = [
    { name: 'Google' },
    { name: 'GitHub' },
    { name: 'Netflix' },
    { name: 'Microsoft' },
    { name: 'Adobe' }
  ];

  return (
    <section className="simple-partners-section">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 mx-auto text-center">
            <h2 className="simple-partners-title">Trusted and backed by Global Investors</h2>
          </div>
        </div>
        <div className="row mt-5">
          <div className="col-12">
            <div className="simple-partners-grid">
              {partners.map((partner, index) => (
                <div key={index} className="simple-partner-item">
                  <div className="partner-placeholder">
                    {partner.name}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PartnersSection;
