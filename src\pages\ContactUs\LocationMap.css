.location-map-container {
  max-width: 1440px;
  margin: 0 auto;
  overflow: hidden;
}

.location-map-text p {
  font-size: 1rem;
  line-height: 1.4;
  font-weight: 400;
}

.location-map-image img {
  width: 100%;
  height: auto;
  object-fit: contain;
  border-radius: 8px;
  display: block;
}

/* Typography */
.h1-bold {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 24px;
}

.h2-bold {
  font-size: 1.25rem;
  font-weight: bold;
  margin-bottom: 16px;
}

/* Optional responsive adjustments */
@media (max-width: 768px) {
  .h1-bold {
    font-size: 2rem;
  }

  .h2-bold {
    font-size: 1.1rem;
  }

  .location-map-text p {
    font-size: 1rem;
    padding: 1;
  }
}
