import React from 'react';
import './Hero.css';
import { Container, Row, Col, Button } from 'react-bootstrap';
import heroElement from '../../assets/images/heroElement.png';

const Hero: React.FC = () => {

  return (
    <section className="hero-section">
      <Container>
        <Row className="align-items-center">
          <Col lg={5} className="hero-content">
            <h1 className="hero-title">
              An Autonomous, Smart, System-Managed Portfolio That Grows Your Wealth
            </h1>
            <p className="hero-description">
              Arth: is a new gen Investing App by Capovex that screens, tracks, and
              executes Investments in security markets for you autonomously using
              Smart strategies, dynamic profiling, and advanced screening.
            </p>
            <p className="hero-description mb-4">
              The all in one app is all you need for Smart Investing.
            </p>
            <Button variant="warning" className="get-started-btn">
              Get Started Now
            </Button>
          </Col>
          <Col lg={7} className="hero-widgets">
            <div className="hero-image-container">
              <img src={heroElement} alt="Hero Element" className="hero-element-image" />
            </div>
          </Col>
        </Row>
      </Container>
    </section>
  );
};

export default Hero;
