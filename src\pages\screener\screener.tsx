import React, { useEffect, useRef, useState } from "react";
import axios from "axios";
import { useNavigate } from "react-router-dom";
import { NavLink } from "react-router-dom";

interface Opportunity {
  symbol: string;
  spot_price: number;
  futures_price: number;
  price_difference: number;
  difference_percentage: number;
}

const ArbitrageScreener: React.FC = () => {
  const navigate = useNavigate();
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(100);
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  const tableRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (tableRef.current) {
      tableRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [currentPage, itemsPerPage]);

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const handlePageSizeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1);
  };

  useEffect(() => {
    const fetchOpportunities = async () => {
      try {
        const response = await axios.get(
          "https://api.capovex.in/arbitrage/top_arbitrage_opportunities?limit=100000",
          {
            auth: {
              username: "admin",
              password: "admin123",
            },
            headers: {
              "Content-Type": "application/json",
              Accept: "application/json",
            },
          }
        );

        if (Array.isArray(response.data.results)) {
          // Convert paisa to rupees here
          const converted = response.data.results.map((item: Opportunity) => ({
            ...item,
            spot_price: item.spot_price / 100,
            futures_price: item.futures_price / 100,
            price_difference: item.price_difference / 100,
          }));
          setOpportunities(converted);
        } else {
          setOpportunities([]);
        }
      } catch {
        setOpportunities([]);
      }
    };

    fetchOpportunities();
    const interval = setInterval(fetchOpportunities, 1000);
    return () => clearInterval(interval);
  }, []);

  // Sort data based on % arbitrage
  const sortedItems = [...opportunities].sort((a, b) =>
    sortOrder === "asc"
      ? a.difference_percentage - b.difference_percentage
      : b.difference_percentage - a.difference_percentage
  );

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = sortedItems.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(opportunities.length / itemsPerPage);

  return (
    <div className="bg-light py-5 min-vh-100">
      <div className="container">
        <div className="bg-white p-4">
          <h2 className="fw-bold mb-1">Arbitrage Screener</h2>
          <p className="text-muted mb-5">
            Analyze potential arbitrage opportunities in the market.
          </p>
          <div className="d-flex justify-content-end align-items-center mb-2">
            <label htmlFor="itemsPerPageSelect" className="me-2 fw-semibold">
              Items per page:
            </label>
            <select
              id="itemsPerPageSelect"
              className="form-select form-select-sm w-auto"
              value={itemsPerPage}
              onChange={handlePageSizeChange}
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
              <option value={200}>200</option>
            </select>
          </div>

          {opportunities.length === 0 ? (
            <p>Loading or no arbitrage opportunities found.</p>
          ) : (
            <>
              <div className="table-responsive mb-6" ref={tableRef}>
                <table className="table align-middle mb-0">
                  <thead className="bg-light text-muted border-bottom">
                    <tr>
                      <th>Stock</th>
                      <th className="py-4 px-3">Current Price</th>
                      <th className="py-4 px-3">Future Price</th>
                      <th className="py-4 px-3">Arbitrage Value</th>
                      <th
                        className="py-4 px-3"
                        style={{ cursor: "pointer" }}
                        onClick={() =>
                          setSortOrder((prev) =>
                            prev === "asc" ? "desc" : "asc"
                          )
                        }
                      >
                        % Arbitrage {sortOrder === "asc" ? "↑" : "↓"}
                      </th>
                      <th className="text-end py-4 px-3">Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentItems.map((item, index) => (
                      <tr
                        className="border-bottom"
                        key={index}
                        style={{ borderSpacing: "0 10px" }}
                      >
                        <td className="fw-bold text-dark py-3 px-3">
                          {item.symbol}
                        </td>
                        <td className="py-4 px-3">
                          ₹{item.spot_price.toFixed(2)}
                        </td>
                        <td className="py-4 px-3">
                          ₹{item.futures_price.toFixed(2)}
                        </td>
                        <td className="py-4 px-3">
                          ₹{item.price_difference.toFixed(2)}
                        </td>
                        <td className="py-4 px-3">
                          {item.difference_percentage.toFixed(2)}%
                        </td>
                        <td className="text-end py-4 px-3">
                          <NavLink
                            to={`/pnl-simulator/${encodeURIComponent(item.symbol)}`}
                            className="text-decoration-none fw-semibold"
                            style={{ color: "#ed7d22" }}
                          >
                            Simulate PnL
                          </NavLink>

                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination Controls */}
              <div className="d-flex justify-content-between align-items-center mt-3 mb-5">
                <button
                  className="btn btn-outline-secondary"
                  onClick={goToPreviousPage}
                  disabled={currentPage === 1}
                >
                  Previous
                </button>
                <span>
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  className="btn btn-outline-secondary"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  Next
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default ArbitrageScreener;
