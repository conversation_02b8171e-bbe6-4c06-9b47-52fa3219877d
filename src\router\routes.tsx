// Centralized route configuration
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import App from '../App';
import ErrorBoundary from '../components/common/ErrorBoundary';
import LoadingSpinner from '../components/common/LoadingSpinner';

// Lazy load components for better performance
const Home = lazy(() => import('../pages/Home'));
const About = lazy(() => import('../pages/About'));
const Login = lazy(() => import('../pages/Login'));
const CreatorAccount = lazy(() => import('../pages/CreatorAccount'));
const ScamAlert = lazy(() => import('../pages/ScamAlert'));
const PrivacyPolicy = lazy(() => import('../pages/PrivacyPolicy'));
const RefundPolicy = lazy(() => import('../pages/RefundPolicy'));
const ABACPolicy = lazy(() => import('../pages/ABACPolicy'));
const CookiesPolicy = lazy(() => import('../pages/CookiesPolicy'));

// Route configuration with metadata
export interface RouteConfig extends RouteObject {
  title?: string;
  description?: string;
  requiresAuth?: boolean;
  roles?: string[];
}

export const routes: RouteConfig[] = [
  {
    path: '/',
    element: <App />,
    errorElement: <ErrorBoundary />,
    children: [
      {
        index: true,
        element: <Home />,
        title: 'Home - Capovex',
        description: 'Smart investing platform for autonomous portfolio management'
      },
      {
        path: 'about',
        element: <About />,
        title: 'About Us - Capovex',
        description: 'Learn about Capovex Research & Analytics'
      },
      {
        path: 'login',
        element: <Login />,
        title: 'Login - Capovex',
        description: 'Sign in to your Capovex account'
      },
      {
        path: 'creator-account',
        element: <CreatorAccount />,
        title: 'Creator Account - Capovex',
        description: 'Create your Capovex creator account',
        requiresAuth: false
      },
      {
        path: 'scam-alert',
        element: <ScamAlert />,
        title: 'Scam Alert - Capovex',
        description: 'Report scam activities and protect the community'
      },
      {
        path: 'privacy-policy',
        element: <PrivacyPolicy />,
        title: 'Privacy Policy - Capovex',
        description: 'Our privacy policy and data protection practices'
      },
      {
        path: 'refund-policy',
        element: <RefundPolicy />,
        title: 'Refund Policy - Capovex',
        description: 'Our refund and cancellation policy'
      },
      {
        path: 'abac-policy',
        element: <ABACPolicy />,
        title: 'ABAC Policy - Capovex',
        description: 'Anti-Bribery and Anti-Corruption policy'
      },
      {
        path: 'cookies-policy',
        element: <CookiesPolicy />,
        title: 'Cookies Policy - Capovex',
        description: 'How we use cookies and tracking technologies'
      }
    ]
  }
];

// Route utilities
export const getRouteByPath = (path: string): RouteConfig | undefined => {
  return routes.find(route => route.path === path);
};

export const getPageTitle = (pathname: string): string => {
  const route = routes
    .flatMap(route => [route, ...(route.children || [])])
    .find(route => route.path === pathname || (route.index && pathname === '/'));
  
  return route?.title || 'Capovex';
};

export const getPageDescription = (pathname: string): string => {
  const route = routes
    .flatMap(route => [route, ...(route.children || [])])
    .find(route => route.path === pathname || (route.index && pathname === '/'));
  
  return route?.description || 'Smart investing platform for autonomous portfolio management';
};
