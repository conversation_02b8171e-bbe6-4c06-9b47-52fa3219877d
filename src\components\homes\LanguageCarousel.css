.language-carousel-section {
  padding: 5rem 0;
  background-color: #FFFBF7;
  position: relative;
  z-index: 2;
  isolation: isolate;
  clear: both;
  margin-top: 0;
}

.language-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #333;
  position: relative;
  z-index: 3;
}

.language-subtitle {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 3rem;
  line-height: 1.6;
  position: relative;
  z-index: 3;
}

.language-header {
  position: relative;
  z-index: 3;
  margin-bottom: 2rem;
}

.carousel-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px 0;
  overflow: hidden;
  background-color: transparent;
}

.language-train-container {
  width: 100%;
  height: 420px; /* Original height for language cards */
  position: relative;
  overflow: hidden;
  background-color: transparent;
  margin: 0;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
}

.language-train {
  display: flex;
  position: relative;
  height: 100%;
  will-change: transform;
  /* No transition property - we're animating with JS for smoother movement */
}

.language-card-train {
  flex: 0 0 auto;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  background-color: transparent;
  width: 280px;
  height: 350px;
  margin: 0 15px; /* Gap between cards */
  pointer-events: none; /* Prevents hover effects from interfering with the carousel */
}

.language-image-full {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 16px;
}

/* Additional carousel styles can be added here */

/* Responsive adjustments */
@media (max-width: 1400px) {
  .language-card-train {
    width: 260px;
    height: 330px;
  }

  .language-card-train.center-card {
    transform: scale(1.2);
  }
}

@media (max-width: 1200px) {
  .language-card-train {
    width: 240px;
    height: 310px;
  }

  .language-card-train.center-card {
    transform: scale(1.2);
  }
}

@media (max-width: 992px) {
  .language-card-train {
    width: 220px;
    height: 280px;
    margin: 0 12px;
  }

  .language-train-container {
    height: 380px;
  }

  .language-card-train.center-card {
    transform: scale(1.18);
  }
}

@media (max-width: 768px) {
  .language-train-container {
    height: 340px;
  }

  .language-card-train {
    width: 200px;
    height: 260px;
    margin: 0 10px;
  }

  .language-card-train.center-card {
    transform: scale(1.15);
  }
}

@media (max-width: 576px) {
  .language-title {
    font-size: 2rem;
  }

  .language-subtitle {
    font-size: 1rem;
  }

  .language-train-container {
    height: 300px;
  }

  .language-card-train {
    width: 180px;
    height: 230px;
    margin: 0 8px; /* Smaller gap on mobile */
  }

  .language-card-train.center-card {
    transform: scale(1.12);
  }
}
