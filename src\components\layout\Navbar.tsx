import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

const Navbar: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <nav className={`navbar navbar-expand-lg navbar-light bg-white py-3 ${scrolled ? 'shadow-sm' : ''}`}>
      <div className="container">
        {/* Logo */}
        <Link className="navbar-brand d-flex align-items-center" to="/">
          <img src="/src/assets/images/Brand.png" alt="Brand Logo" height="40" />
        </Link>

        {/* Mobile Toggle Button */}
        <button
          className="navbar-toggler border-0"
          type="button"
          onClick={() => setIsOpen(!isOpen)}
          aria-controls="navbarContent"
          aria-expanded={isOpen}
          aria-label="Toggle navigation"
        >
          <span className="navbar-toggler-icon"></span>
        </button>

        {/* Navbar Content */}
        <div className={`collapse navbar-collapse ${isOpen ? 'show' : ''}`} id="navbarContent">
          {/* Center Menu */}
          <ul className="navbar-nav mx-auto mb-2 mb-lg-0">
            <li className="nav-item mx-1">
              <Link className="nav-link active" aria-current="page" to="/">Home</Link>
            </li>
            <li className="nav-item dropdown mx-1">
              <button
                className="nav-link dropdown-toggle btn btn-link"
                id="ecosystemDropdown"
                data-bs-toggle="dropdown"
                aria-expanded="false"
                style={{ background: 'none', border: 'none', padding: '0.5rem 1rem' }}
              >
                Our Ecosystem
              </button>
              <ul className="dropdown-menu border-0 shadow-sm" aria-labelledby="ecosystemDropdown">
                <li><Link className="dropdown-item" to="/products">Products</Link></li>
                <li><Link className="dropdown-item" to="/services">Services</Link></li>
                <li><Link className="dropdown-item" to="/solutions">Solutions</Link></li>
              </ul>
            </li>
            <li className="nav-item mx-1">
              <Link className="nav-link" to="/pricing">Pricing</Link>
            </li>
            <li className="nav-item mx-1">
              <Link className="nav-link" to="/investors">Investors</Link>
            </li>
            <li className="nav-item mx-1">
              <Link className="nav-link" to="/contact-us">Contact Us</Link>
            </li>
          </ul>

          {/* Right Buttons */}
          <div className="d-flex align-items-center">
            <Link
              to="/login"
              className="btn btn-outline-secondary me-3 d-flex align-items-center px-3 py-2"
            >
              Sign In
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-arrow-right ms-2" viewBox="0 0 16 16">
                <path fillRule="evenodd" d="M1 8a.5.5 0 0 1 .5-.5h11.793l-3.147-3.146a.5.5 0 0 1 .708-.708l4 4a.5.5 0 0 1 0 .708l-4 4a.5.5 0 0 1-.708-.708L13.293 8.5H1.5A.5.5 0 0 1 1 8z"/>
              </svg>
            </Link>
            <Link
              to="/creator-account"
              className="btn btn-warning text-white px-3 py-2"
            >
              Sign Up
            </Link>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
