import React, { useState } from 'react';
import './ScamAlert.css';

const ScamAlert: React.FC = () => {
  console.log('ScamAlert component is rendering');

  const [formData, setFormData] = useState({
    activeState: '',
    websiteUrl: '',
    telegramChannel: '',
    scammerAdvertising: '',
    cyberCellPolice: '',
    cyberCellPoliceReported: '',
    financialLoss: '',
    scamDetails: '',
    evidenceFiles: null as FileList | null
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFormData(prev => ({
        ...prev,
        evidenceFiles: e.target.files
      }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Form submitted:', formData);
    // You can add API call or other submission logic here
  };

  return (
    <div className="scam-alert-container">
      <div className="scam-alert-header">
        <div className="container text-center">
          <h1>SCAM ALERT</h1>
          <p className="subtitle">Important Notice to All Users</p>
        </div>
      </div>

      <div className="scam-alert-content container">
        <div className="row">
          {/* Left Column - Information */}
          <div className="col-lg-6">
            <div className="alert-info-section">
              <p className="intro-text">
                At Capovex Research and Analytics Pvt. Ltd., we prioritize the security and integrity of
                our services, and the trust you place in us. Recently, it has come to our attention that
                certain individuals or entities might be using our brand name to create groups or
                channels across various social media platforms, including but not limited to WhatsApp,
                Telegram, and other messaging services.
              </p>

              <section className="info-section">
                <h2>Official Communication Channels</h2>
                <p>
                  We would like to clarify to all our users and the public that Capovex Research and
                  Analytics Pvt. Ltd. does not operate any official groups or channels on these platforms
                  for the purpose of providing investment tips, stock recommendations, or portfolio
                  management advice. Any such groups or channels claiming to represent our company
                  and we strictly advise by the rules and regulations set by the Securities Exchange Board
                  of India (SEBI).
                </p>
              </section>

              <section className="info-section">
                <h2>Our Commitment</h2>
                <p><strong>Capovex does not endorse or engage in:</strong></p>
                <ol>
                  <li>Trading suggestions</li>
                  <li>Investment tips</li>
                  <li>Financial advisory services outside of our official platforms and products.</li>
                </ol>
              </section>

              <section className="info-section">
                <h2>Reporting Suspicious Activities</h2>
                <p>
                  If you encounter any group, channel, or entity falsely using the Capovex brand name, we
                  urge you to promptly report it using the form provided. Prompt reporting can help
                  protect other potential victims and contribute to the ongoing security of
                  all users.
                </p>
              </section>

              <section className="info-section">
                <h2>Stay Informed</h2>
                <p>
                  To stay updated on legitimate information and announcements from Capovex, please
                  refer to our official website and verified communication outlets. We value your vigilance
                  and cooperation in safeguarding the authenticity of our brand and services.
                </p>
                <p>
                  For any questions or additional information, feel free to contact our support team at:
                  <strong> <EMAIL></strong>
                </p>
              </section>
            </div>
          </div>

          {/* Right Column - Report Form */}
          <div className="col-lg-6">
            <div className="report-form-section">
              <form onSubmit={handleSubmit} className="scam-report-form">

                {/* Link to Scam Platform */}
                <div className="form-group-exact">
                  <label className="form-label-exact">Link to Scam Platform*</label>
                  <input
                    type="text"
                    id="activeState"
                    name="activeState"
                    value={formData.activeState}
                    onChange={handleInputChange}
                    className="form-input-exact"
                    placeholder="Active State"
                    required
                  />
                  <p className="form-help-text">Enter Link of Website, Telegram Channel or Whatsapp Group</p>
                </div>

                {/* Link to Scam Advertisements */}
                <div className="form-group-exact">
                  <label className="form-label-exact">Link to Scam Advertisements*</label>
                  <input
                    type="text"
                    id="telegramChannel"
                    name="telegramChannel"
                    value={formData.telegramChannel}
                    onChange={handleInputChange}
                    className="form-input-exact"
                    placeholder="Active State"
                    required
                  />
                  <p className="form-help-text">Is the Scammer Advertising? Share the Link of Advertisement.</p>
                </div>

                {/* Have you Reported the Scammer to Cyber Cell / Police */}
                <div className="form-group-exact">
                  <label className="form-label-exact">Have you Reported the Scammer to Cyber Cell / Police*</label>
                  <select
                    id="cyberCellPolice"
                    name="cyberCellPolice"
                    value={formData.cyberCellPolice}
                    onChange={handleInputChange}
                    className="form-select-exact"
                    required
                  >
                    <option value="">Have you Reported the Scammer to Cyber Cell / Police</option>
                    <option value="yes">Yes</option>
                    <option value="no">No</option>
                  </select>
                </div>

                {/* Did You suffer any financial losses */}
                <div className="form-group-exact">
                  <label className="form-label-exact">Did You suffer any financial losses*</label>
                  <select
                    id="financialLoss"
                    name="financialLoss"
                    value={formData.financialLoss}
                    onChange={handleInputChange}
                    className="form-select-exact"
                    required
                  >
                    <option value="">Did You suffer any financial losses</option>
                    <option value="yes">Yes</option>
                    <option value="no">No</option>
                  </select>
                </div>

                {/* Details of the Scam */}
                <div className="form-group-exact">
                  <label className="form-label-exact">Details of the Scam and how did you find it is a scam*</label>
                  <textarea
                    id="scamDetails"
                    name="scamDetails"
                    value={formData.scamDetails}
                    onChange={handleInputChange}
                    className="form-textarea-exact"
                    rows={4}
                    placeholder="Active State"
                    required
                  ></textarea>
                  <p className="form-help-text">This is the description area</p>
                </div>

                {/* Upload Evidences */}
                <div className="form-group-exact">
                  <label className="form-label-exact">Upload Evidences</label>
                  <p className="form-help-text">You can upload up to 5 files max</p>
                  <div className="file-upload-exact">
                    <input
                      type="file"
                      id="evidenceFiles"
                      name="evidenceFiles"
                      onChange={handleFileChange}
                      className="file-input-hidden"
                      multiple
                      accept=".jpg,.jpeg,.png,.pdf,.doc,.docx"
                    />
                    <div className="file-upload-area-exact" onClick={() => document.getElementById('evidenceFiles')?.click()}>
                      <div className="upload-icon-exact">
                        <i className="bi bi-cloud-upload"></i>
                      </div>
                      <p className="upload-main-text">Drag your file(s) to start uploading</p>
                      <p className="upload-or-text">OR</p>
                      <button type="button" className="browse-files-btn">Browse files</button>
                    </div>
                  </div>
                  <p className="form-help-text">Only support .jpg, .png and .svg and zip files</p>
                </div>

                {/* reCAPTCHA */}
                <div className="form-group-exact">
                  <div className="captcha-container">
                    <div className="g-recaptcha" data-sitekey="your-site-key"></div>
                  </div>
                </div>

                {/* Submit Button */}
                <div className="form-group-exact">
                  <button type="submit" className="submit-button-exact">
                    Send <i className="bi bi-arrow-right"></i>
                  </button>
                </div>

              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ScamAlert;
