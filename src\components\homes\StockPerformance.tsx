import React from 'react';
import './StockPerformance.css';
import { Fa<PERSON>rrowRight, FaEllipsisV, FaCloudUploadAlt } from 'react-icons/fa';

const StockPerformance: React.FC = () => {
  return (
    <section className="stock-performance-section">
      <div className="container">
        <div className="stock-performance-content">
          {/* Left side - Text content */}
          <div className="performance-info">
            <span className="meet-tag">Meet Stellar</span>
            <h2 className="section-title">
              Provide powerful<br />
              solutions at all times
            </h2>
            <p className="section-description">
              Stellar is more than just a SaaS and technology template—it's a
              complete digital transformation solution.
            </p>
            
            <ul className="feature-list">
              <li className="feature-item">
                <div className="feature-dot"></div>
                <p className="feature-text">
                  Access research and analytics in multiple regional languages
                </p>
              </li>
              <li className="feature-item">
                <div className="feature-dot"></div>
                <p className="feature-text">
                  Gain insights from markets, industries, and diverse asset classes.
                </p>
              </li>
              <li className="feature-item">
                <div className="feature-dot"></div>
                <p className="feature-text">
                  Utilize tools designed for both amateur and professional investors
                </p>
              </li>
              <li className="feature-item">
                <div className="feature-dot"></div>
                <p className="feature-text">
                  Streamlines complex studies into a connected ecosystem
                </p>
              </li>
            </ul>
            
            <div className="trial-button-container">
              <button className="trial-button">
                Get Free Trial
                <FaArrowRight className="button-arrow" />
              </button>
            </div>
          </div>
          
          {/* Right side - Stock cards */}
          <div className="stock-cards-container">
            {/* Stock Details Card */}
            <div className="stock-card details-card">
              <div className="card-header">
                <h3 className="card-title">Stock Details</h3>
                <div className="time-filters">
                  <button className="time-filter active">10M</button>
                  <button className="time-filter">1H</button>
                  <button className="time-filter">1D</button>
                </div>
              </div>
              
              <div className="stock-list">
                {/* INFY Stock */}
                <div className="stock-item">
                  <div className="stock-info">
                    <span className="stock-symbol">INFY</span>
                    <div className="stock-change positive">
                      <span className="change-value">+₹2.78</span>
                    </div>
                  </div>
                  <div className="stock-price">₹1,480.00</div>
                  <div className="stock-chart">
                    <svg viewBox="0 0 100 30" className="chart-svg positive">
                      <path d="M0,15 L10,10 L20,20 L30,5 L40,15 L50,10 L60,20 L70,15 L80,5 L90,10 L100,15" />
                    </svg>
                  </div>
                </div>
                
                {/* SBIN Stock */}
                <div className="stock-item">
                  <div className="stock-info">
                    <span className="stock-symbol">SBIN</span>
                    <div className="stock-change negative">
                      <span className="change-value">-₹1.34</span>
                    </div>
                  </div>
                  <div className="stock-price">₹790.00</div>
                  <div className="stock-chart">
                    <svg viewBox="0 0 100 30" className="chart-svg negative">
                      <path d="M0,15 L10,20 L20,10 L30,15 L40,5 L50,15 L60,20 L70,10 L80,15 L90,20 L100,15" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Performance Card */}
            <div className="stock-card performance-card">
              <div className="performance-icon">
                <FaCloudUploadAlt className="cloud-icon" />
              </div>
              
              <div className="performance-details">
                <div className="performance-header">
                  <div className="amount-container">
                    <h3 className="amount">₹ 16,248.50</h3>
                    <button className="options-button">
                      <FaEllipsisV />
                    </button>
                  </div>
                  <p className="performance-description">Realised gains This months</p>
                </div>
                
                <div className="progress-bars">
                  <div className="progress-item">
                    <div className="progress-bar gray">
                      <div className="progress-fill" style={{ width: '44%' }}></div>
                    </div>
                    <span className="progress-value">44%</span>
                  </div>
                  
                  <div className="progress-item">
                    <div className="progress-bar orange">
                      <div className="progress-fill" style={{ width: '96%' }}></div>
                    </div>
                    <span className="progress-value">96%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StockPerformance;
