import React from 'react';
import './FeaturesSection.css';

// Import icon images
import firstIcon from '../../assets/icons/first.png';
import secondIcon from '../../assets/icons/second.png';
import thirdIcon from '../../assets/icons/third.png';
import fourthIcon from '../../assets/icons/fourth.png';
import fifthIcon from '../../assets/icons/fifth.png';
import sixthIcon from '../../assets/icons/sixth.png';


const FeatureCard: React.FC<{
  iconSrc: string;
  title: string;
  description: string;
}> = ({ iconSrc, title, description }) => {
  return (
    <div className="feature-card">
      <div className="feature-icon">
        <img src={iconSrc} alt={title} />
      </div>
      <h3 className="feature-title">{title}</h3>
      <p className="feature-description">{description}</p>
    </div>
  );
};

const FeaturesSection: React.FC = () => {
  const featureDescription = "Receive personalized alerts tailored to your investment interests and portfolio, ensuring you never miss any.";

  return (
    <section className="features-section">
      <div className="container">
        <div className="features-header">
          <h2 className="features-title">
            A Smart way to Invest in Securities &<br />
            Mutual Funds Based on User Persona
          </h2>
          <p className="features-subtitle">
            Analyze global market trends and segments to see how they impact<br />
            your portfolio, helping you make informed investment decisions
          </p>
        </div>

        <div className="features-container">
          <div className="features-row">
            <div className="feature-col">
              <FeatureCard
                iconSrc={firstIcon}
                title="Dynamic User Persona"
                description={featureDescription}
              />
            </div>
            <div className="feature-col">
              <FeatureCard
                iconSrc={secondIcon}
                title="AI Driven Portfolios"
                description={featureDescription}
              />
            </div>
            <div className="feature-col">
              <FeatureCard
                iconSrc={thirdIcon}
                title="Customized Alerts"
                description={featureDescription}
              />
            </div>
          </div>

          <div className="features-row">
            <div className="feature-col">
              <FeatureCard
                iconSrc={fourthIcon}
                title="Arth Shield Protection"
                description={featureDescription}
              />
            </div>
            <div className="feature-col">
              <FeatureCard
                iconSrc={fifthIcon}
                title="Real Time Monitoring"
                description={featureDescription}
              />
            </div>
            <div className="feature-col">
              <FeatureCard
                iconSrc={sixthIcon}
                title="24 Hrs Available Experts"
                description={featureDescription}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
