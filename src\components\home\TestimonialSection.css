.testimonial-section {
  padding: 5rem 0;
  background-color: #FFFBF7;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.testimonial-header {
  text-align: center;
  max-width: 700px;
  margin: 0 auto 3rem;
}

.testimonial-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1rem;
}

.testimonial-subtitle {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #666;
}

.testimonial-carousel {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 60px;
}

.carousel-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #fff;
  border: 1px solid #ddd;
  color: #333;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
}

.carousel-arrow-left {
  left: 0;
}

.carousel-arrow-right {
  right: 0;
}

.carousel-arrow:hover {
  background-color: #f5f5f5;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.testimonial-cards {
  display: flex;
  gap: 2rem;
  width: 100%;
  max-width: 1000px;
  transition: all 0.5s ease;
}

.testimonial-cards.animating {
  opacity: 0.7;
  transform: scale(0.98);
}

.testimonial-card {
  flex: 1;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 10px;
  padding: 2.5rem 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, opacity 0.3s ease, box-shadow 0.3s ease;
  min-height: 320px;
  position: relative;
}

.testimonial-card:hover {
  transform: translateY(-5px);
}

.testimonial-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #FF6B00;
  color: white;
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 8px rgba(255, 107, 0, 0.3);
}

.testimonial-quote {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.testimonial-text {
  font-size: 1rem;
  line-height: 1.6;
  color: #666;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.testimonial-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

@media (max-width: 992px) {
  .testimonial-carousel {
    padding: 0 50px;
  }

  .testimonial-cards {
    flex-direction: column;
    max-width: 500px;
  }

  .testimonial-card {
    margin-bottom: 1.5rem;
  }

  .carousel-arrow {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .testimonial-title {
    font-size: 2rem;
  }

  .testimonial-card {
    padding: 1.5rem;
    min-height: auto;
  }

  .testimonial-carousel {
    padding: 0 40px;
  }

  .carousel-arrow {
    width: 36px;
    height: 36px;
  }
}
