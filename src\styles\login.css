.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - 76px);
  padding: 2rem 1rem;
  background-color: #FFFBF7;
}

.login-form {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 2.5rem;
  width: 100%;
  max-width: 500px;
}

.login-form h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
}

.login-form p {
  color: #666;
}

.form-control {
  padding: 0.75rem 1rem;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.form-control:focus {
  border-color: #FF7A45;
  box-shadow: 0 0 0 0.2rem rgba(255, 122, 69, 0.25);
}

.form-text {
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.form-check-input:checked {
  background-color: #FF7A45;
  border-color: #FF7A45;
}

.forgot-password {
  color: #FF7A45;
  text-decoration: none;
  font-size: 0.9rem;
}

.forgot-password:hover {
  text-decoration: underline;
}

.login-btn {
  background-color: #FF7A45;
  border-color: #FF7A45;
  padding: 0.75rem;
  font-weight: 500;
  border-radius: 4px;
  color: white;
}

.login-btn:hover, .login-btn:focus {
  background-color: #e86a38;
  border-color: #e86a38;
  color: white;
}

.signup-text {
  font-size: 0.9rem;
  color: #666;
}

.signup-link {
  color: #FF7A45;
  text-decoration: none;
  font-weight: 500;
}

.signup-link:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .login-form {
    padding: 1.5rem;
  }
}
