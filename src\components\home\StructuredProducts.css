.structured-products-section {
  padding: 5rem 0;
  background-color: #FFFBF7;
  overflow: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.structured-products-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 4rem;
}

/* Left side - Cards */
.product-cards {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 380px;
}

.product-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 1.5rem;
  border: 1px solid #e0e0e0;
}

/* Arbitrage Card Styles */
.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #333;
}

.card-details {
  margin-bottom: 1rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
}

.detail-label {
  color: #666;
}

.detail-value {
  font-weight: 500;
  color: #333;
}

.progress-container {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background-color: #f0f0f0;
  border-radius: 2px;
  overflow: hidden;
  margin-right: 10px;
}

.progress-fill {
  height: 100%;
  background-color: #FF6B00;
  border-radius: 2px;
}

.progress-text {
  font-weight: 500;
  color: #333;
  font-size: 0.85rem;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
}

.avatars {
  display: flex;
  align-items: center;
}

.avatar-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 1px solid white;
  margin-right: -8px;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-size: 0.7rem;
}

.avatar-count {
  font-size: 0.75rem;
  font-weight: 500;
  color: #666;
  margin-left: 5px;
}

.join-button {
  background-color: transparent;
  border: none;
  color: #FF6B00;
  font-weight: 500;
  cursor: pointer;
  font-size: 0.85rem;
  padding: 0.25rem 0.5rem;
}

/* Options Card Styles */
.positions-text {
  color: #666;
  font-size: 0.85rem;
  margin-bottom: 1rem;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.option-details {
  display: flex;
  align-items: center;
}

.option-exchange {
  display: inline-block;
  width: 24px;
  text-align: left;
  font-weight: 500;
  font-size: 0.8rem;
  color: #333;
}

.option-name {
  font-weight: 500;
  color: #333;
  margin-left: 0.5rem;
  font-size: 0.8rem;
}

.option-price {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.arrow-icon {
  color: #FF6B00;
  font-size: 0.7rem;
}

.price {
  font-weight: 500;
  color: #333;
  font-size: 0.8rem;
}

/* Right side - Text content */
.product-info {
  flex: 1;
  max-width: 550px;
}

.exclusive-tag {
  display: inline-block;
  color: #FF6B00;
  font-weight: 500;
  font-size: 0.9rem;
  margin-bottom: 0.75rem;
}

.section-title {
  font-size: 2.2rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: #333;
}

.section-description {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.25rem;
  position: relative;
}

.feature-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #FF6B00;
  margin-right: 1rem;
  margin-top: 0.5rem;
}

.feature-text {
  color: #555;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

.trial-button-container {
  margin-top: 1.5rem;
}

.trial-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: transparent;
  border: 1px solid #333;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-weight: 500;
  font-size: 0.9rem;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

.trial-button:hover {
  background-color: #333;
  color: white;
}

.button-arrow {
  font-size: 0.7rem;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .structured-products-content {
    gap: 3rem;
  }
}

@media (max-width: 992px) {
  .structured-products-content {
    flex-direction: column;
    gap: 3rem;
    align-items: center;
  }
  
  .product-info {
    max-width: 100%;
  }
  
  .product-cards {
    max-width: 450px;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .structured-products-section {
    padding: 4rem 0;
  }
  
  .section-title {
    font-size: 1.8rem;
  }
}

@media (max-width: 576px) {
  .structured-products-section {
    padding: 3rem 0;
  }
  
  .section-title {
    font-size: 1.6rem;
  }
}
