// Custom hook for scam report form logic
import { useState, useCallback } from 'react';
import { ScamReportData } from '../components/forms/ScamReportForm';

interface FormErrors {
  scamPlatformLink?: string;
  advertisementLink?: string;
  reportedToCyberCell?: string;
  financialLoss?: string;
  scamDetails?: string;
  evidenceFiles?: string;
}

interface UseScamReportFormProps {
  onSubmit: (data: ScamReportData) => Promise<void>;
  initialData?: Partial<ScamReportData>;
}

export const useScamReportForm = ({ onSubmit, initialData }: UseScamReportFormProps) => {
  const [formData, setFormData] = useState<ScamReportData>({
    scamPlatformLink: initialData?.scamPlatformLink || '',
    advertisementLink: initialData?.advertisementLink || '',
    reportedToCyberCell: initialData?.reportedToCyberCell || false,
    financialLoss: initialData?.financialLoss || false,
    scamDetails: initialData?.scamDetails || '',
    evidenceFiles: initialData?.evidenceFiles || null,
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const validateField = useCallback((name: keyof ScamReportData, value: any): string | undefined => {
    switch (name) {
      case 'scamPlatformLink':
        if (!value?.trim()) return 'Platform link is required';
        if (!isValidUrl(value)) return 'Please enter a valid URL';
        break;
      
      case 'advertisementLink':
        if (!value?.trim()) return 'Advertisement link is required';
        if (!isValidUrl(value)) return 'Please enter a valid URL';
        break;
      
      case 'reportedToCyberCell':
        if (value === '' || value === undefined) return 'Please select an option';
        break;
      
      case 'financialLoss':
        if (value === '' || value === undefined) return 'Please select an option';
        break;
      
      case 'scamDetails':
        if (!value?.trim()) return 'Scam details are required';
        if (value.trim().length < 10) return 'Please provide more details (minimum 10 characters)';
        break;
      
      case 'evidenceFiles':
        if (value && value.length > 5) return 'Maximum 5 files allowed';
        break;
    }
    return undefined;
  }, []);

  const isValidUrl = (string: string): boolean => {
    try {
      new URL(string);
      return true;
    } catch (_) {
      return false;
    }
  };

  const validateForm = useCallback((): boolean => {
    const newErrors: FormErrors = {};
    let isValid = true;

    (Object.keys(formData) as Array<keyof ScamReportData>).forEach((key) => {
      const error = validateField(key, formData[key]);
      if (error) {
        newErrors[key] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  }, [formData, validateField]);

  const handleInputChange = useCallback((
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));

    // Clear error when user starts typing
    if (errors[name as keyof FormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }

    // Mark field as touched
    setTouched(prev => ({
      ...prev,
      [name]: true
    }));
  }, [errors]);

  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    setFormData(prev => ({
      ...prev,
      evidenceFiles: files
    }));

    // Validate file count
    if (files && files.length > 5) {
      setErrors(prev => ({
        ...prev,
        evidenceFiles: 'Maximum 5 files allowed'
      }));
    } else {
      setErrors(prev => ({
        ...prev,
        evidenceFiles: undefined
      }));
    }
  }, []);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      await onSubmit(formData);
      // Reset form on successful submission
      resetForm();
    } catch (error) {
      console.error('Form submission error:', error);
      // Handle submission error
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, validateForm, onSubmit]);

  const resetForm = useCallback(() => {
    setFormData({
      scamPlatformLink: '',
      advertisementLink: '',
      reportedToCyberCell: false,
      financialLoss: false,
      scamDetails: '',
      evidenceFiles: null,
    });
    setErrors({});
    setTouched({});
  }, []);

  return {
    formData,
    errors,
    isSubmitting,
    touched,
    handleInputChange,
    handleFileChange,
    handleSubmit,
    resetForm,
    validateForm
  };
};
