import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import '../styles/login.css';

const Login: React.FC = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    rememberMe: false
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prevState => ({
      ...prevState,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle login logic here
    console.log('Login submitted:', formData);
  };

  return (
    <div className="login-container">
      <div className="login-form">
        <h1 className="text-center mb-2">Welcome Back</h1>
        <p className="text-center mb-4">Please log in to continue</p>

        <form onSubmit={handleSubmit}>
          <div className="mb-3">
            <label htmlFor="username" className="form-label">User</label>
            <input
              type="text"
              className="form-control"
              id="username"
              name="username"
              placeholder="Placeholder"
              value={formData.username}
              onChange={handleChange}
              required
            />
          </div>

          <div className="mb-3">
            <label htmlFor="password" className="form-label">Password</label>
            <input
              type="password"
              className="form-control"
              id="password"
              name="password"
              placeholder="Placeholder"
              value={formData.password}
              onChange={handleChange}
              required
            />
            <small className="form-text text-muted">It must be a combination of minimum 8 letters, numbers, and symbols.</small>
          </div>

          <div className="d-flex justify-content-between align-items-center mb-4">
            <div className="form-check">
              <input
                type="checkbox"
                className="form-check-input"
                id="rememberMe"
                name="rememberMe"
                checked={formData.rememberMe}
                onChange={handleChange}
              />
              <label className="form-check-label" htmlFor="rememberMe">Remember me</label>
            </div>
            <Link to="/forgot-password" className="forgot-password">Forgot Password?</Link>
          </div>

          <div className="d-grid mb-4">
            <button type="submit" className="btn login-btn">Log In</button>
          </div>

          <div className="text-center">
            <p className="signup-text">
              No account yet? <Link to="/creator-account" className="signup-link">Sign Up</Link>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Login;
