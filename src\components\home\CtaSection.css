.cta-section {
  padding: 6rem 0;
  background-color: #FFFBF7;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.cta-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4rem;
}

.cta-text {
  flex: 1;
  max-width: 420px;
}

.cta-title {
  font-size: 2.75rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 1.25rem;
}

.cta-description {
  font-size: 1.05rem;
  line-height: 1.7;
  color: #666;
  margin-bottom: 2.75rem;
  font-weight: 400;
}

.app-buttons {
  display: flex;
  gap: 1.5rem;
}

.app-button {
  display: flex;
  align-items: center;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  text-decoration: none;
  transition: transform 0.3s ease, opacity 0.3s ease;
  background-color: #000;
  color: white;
  min-width: 220px;
  height: 60px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.app-button:hover {
  transform: translateY(-3px);
  opacity: 0.9;
}

.app-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.app-icon svg {
  width: 28px;
  height: 28px;
}

.google-play-icon {
  width: 28px;
  height: 28px;
  object-fit: contain;
  filter: brightness(0) invert(1); /* Make the icon white to match the App Store icon */
}

.app-text {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.app-small {
  font-size: 0.75rem;
  font-weight: 400;
  letter-spacing: 0.02em;
}

.app-large {
  font-size: 1.2rem;
  font-weight: 600;
  letter-spacing: 0.01em;
}

.cta-image {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.app-screenshot {
  max-width: 100%;
  height: auto;
  max-height: 550px;
  object-fit: contain;
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
}

@media (max-width: 992px) {
  .cta-content {
    flex-direction: column;
    text-align: center;
  }

  .cta-text {
    max-width: 100%;
  }

  .app-buttons {
    justify-content: center;
  }

  .cta-image {
    justify-content: center;
  }
}
