{"name": "<PERSON><PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@popperjs/core": "^2.11.8", "axios": "^1.9.0", "bootstrap": "^5.3.6", "bootstrap-icons": "^1.12.1", "react": "^19.1.0", "react-bootstrap": "^2.10.9", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.0", "recharts": "^2.15.3", "sass": "^1.87.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}