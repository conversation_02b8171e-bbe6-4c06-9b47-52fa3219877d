/* Navbar Styles */
.navbar {
  transition: box-shadow 0.3s ease;
}

.navbar.shadow-sm {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.navbar-brand {
  color: #333;
}

.nav-link {
  color: #4a4a4a !important;
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: #ff7a45 !important;
}

.nav-link.active {
  color: #ff7a45 !important;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 2px;
  background-color: #ff7a45;
}

.dropdown-toggle::after {
  vertical-align: middle;
  margin-left: 0.3em;
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-left: 0.3em solid transparent;
}

.dropdown-menu {
  border-radius: 8px;
  margin-top: 10px;
  padding: 8px 0;
}

.dropdown-item {
  padding: 8px 16px;
  color: #4a4a4a;
  transition: all 0.2s ease;
}

.dropdown-item:hover {
  background-color: rgba(255, 122, 69, 0.1);
  color: #ff7a45;
}

.btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-outline-secondary {
  border-color: #e0e0e0;
  color: #4a4a4a;
}

.btn-outline-secondary:hover {
  background-color: #f8f9fa;
  border-color: #e0e0e0;
  color: #ff7a45;
}

.btn-warning {
  background-color: #ff7a45;
  border-color: #ff7a45;
  box-shadow: 0 2px 8px rgba(255, 122, 69, 0.3);
}

.btn-warning:hover {
  background-color: #ff6a30;
  border-color: #ff6a30;
  box-shadow: 0 4px 12px rgba(255, 122, 69, 0.4);
  transform: translateY(-1px);
}

@media (max-width: 991.98px) {
  .navbar-nav {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  .d-flex {
    margin-top: 1rem;
    justify-content: center;
  }

  .nav-link.active::after {
    display: none;
  }
}
