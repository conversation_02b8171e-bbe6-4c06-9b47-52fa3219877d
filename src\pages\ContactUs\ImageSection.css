.image-section {
  position: relative;
  height: 450px;
  overflow: hidden;
  color: white;
  font-family: sans-serif;
}

/* Background image */
.bg-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

/* Content layout */
.content-container {
  position: relative;
  z-index: 2;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  padding: 0 5%;
}

/* Left side text */
.text-content {
  max-width: 50%;
}

/* Right side map - centered */
.map-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 50%;
}

.map-overlay {
  width: 60%; /* Increased from 300px to 400px */
  height: 110%;
  border-radius: 12px;
  
}

