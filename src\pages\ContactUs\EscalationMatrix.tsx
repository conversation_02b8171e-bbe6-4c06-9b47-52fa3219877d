import React from "react";
import "bootstrap/dist/css/bootstrap.min.css";
import { <PERSON>, But<PERSON> } from "react-bootstrap";
import "./EscalationMatrix.css";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faClock } from "@fortawesome/free-solid-svg-icons";

interface ContactDetail {
  role: string;
  name: string;
  email: string;
  time: string;
  phone: string;
}

interface EscalationOption {
  title: string;
  text: string;
  link: string;
  variant: string;
}

const contactDetails: ContactDetail[] = [
  {
    role: "Customer Care Executive",
    name: "Mr. <PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    time: "10AM - 5PM",
    phone: "0755-4311100 Ext-12",
  },
  {
    role: "Head of Customer care",
    name: "Mr. <PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    time: "10AM - 5PM",
    phone: "0755-4311100 Ext-15",
  },
  {
    role: "Compliance Officer",
    name: "Mr. <PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    time: "10AM - 5PM",
    phone: "0755-4311100 Ext-34",
  },
  {
    role: "CEO",
    name: "Mr. Jatin Jayant Mohgaonkar",
    email: "<EMAIL>",
    time: "10AM - 5PM",
    phone: "0755-4311100 Ext-21",
  },
];

const escalationOptions: EscalationOption[] = [
  {
    title: "SEBI",
    text: "The SEBI online Complaints Redress System (SCORES) allows investors to lodge grievances against any SEBI-registered intermediary. Use SCORES to ensure accountability, escalate unresolved complaints.",
    link: "#",
    variant: "warning",
  },
  {
    title: "CDSL",
    text: "CDSL provides a dedicated grievance portal for demat-account holders to report issues such as statement discrepancies, pledge/release failures, or depository participant lapses.",
    link: "#",
    variant: "success",
  },
  {
    title: "NSE",
    text: "NICEPLUS platform caters to complaints related to trade execution, settlement delays, or broker misconduct. Through a structured form, investors can complain, and follow up directly with NSE.",
    link: "#",
    variant: "info",
  },
  {
    title: "BSE",
    text: "BSE e-Complaint system enables resolution of grievances tied to BSE-listed securities—trade mismatches, settlement irregularities, or corporate action disputes. Investors submit details online.",
    link: "#",
    variant: "secondary",
  },
];

const EscalationMatrix: React.FC = () => {
  return (
    <div className="container location-map-container py-5">
      <h2 className="h1-bold mb-4 pb-4">Escalation Matrix</h2>
      <div className="row d-flex align-items-stretch">
        {/* Left Column - Contact Details */}
        <div className="col-md-6 d-flex flex-column pe-md-4">
          <ul className="timeline-list ps-3 mb-4 flex-grow-1">
            {contactDetails.map((contact, index) => (
              <li key={index} className="mb-5 position-relative">
                <span className="timeline-dot position-absolute top-1 start-0 translate-middle rounded-circle"></span>
               <div className="ps-5"> 
                 <strong className="d-block mb-1 contact-role fs-5">{contact.role}</strong>
                  <span className="d-block mb-1 contact-name fs-6">{contact.name}</span>
                  <a href={`mailto:${contact.email}`} className="d-block mb-1 link-style">
                   {contact.email}
                  </a>
                  <span className="d-block mb-1">
                   <FontAwesomeIcon icon={faClock} className="me-1" />
                   {contact.time} |{" "}
                    <a href={`tel:${contact.phone.split(" ")[0]}`} className="link-style">
                     {contact.phone}
                    </a>
                  </span> 
                </div>
              </li>

            ))}
          </ul>
        </div>

        {/* Right Column - Escalation Options */}
        <div className="col-md-6 d-flex flex-column ps-md-4">
          <p className="mb-4">
            In absence of a response/ complaint not addressed to your satisfaction,
            you may lodge a complaint with:
          </p>
          <div className="row flex-grow-1">
            {escalationOptions.map((option, idx) => (
              <div className="col-md-6 mb-4" key={idx}>
                <Card className={`custom-card ${idx % 2 === 0 ? "strip-orange" : "strip-green"}`}>
                  <Card.Body>
                    <Card.Title>{option.title}</Card.Title>
                    <Card.Text>{option.text}</Card.Text>
                    <Button className="custom-button" href={option.link}>
                      Go to {option.title}
                    </Button>
                  </Card.Body>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </div>

      <p className="mt-4">
        After you’ve exhausted all the options available for resolving your grievance,
        if you’re still not satisfied with the outcome, you can initiate the dispute resolution
        process through the ODR Portal{" "}
        <a href="https://smartodr.in" className="link-style">https://smartodr.in</a>
      </p>
    </div>
  );
};

export default EscalationMatrix;
