import React from 'react';
import './StructuredProducts.css';
import { FaArrowRight, FaUser } from 'react-icons/fa';

const StructuredProducts: React.FC = () => {
  return (
    <section className="structured-products-section">
      <div className="container">
        <div className="structured-products-content">
          {/* Left side - Cards */}
          <div className="product-cards">
            {/* Arbitrage Group Card */}
            <div className="product-card arbitrage-card">
              <h3 className="card-title">Arbitrage Group A</h3>
              <div className="card-details">
                <div className="detail-row">
                  <span className="detail-label">Margin Required:</span>
                  <span className="detail-value">₹ 1.5 Million</span>
                </div>
                <div className="detail-row">
                  <span className="detail-label">Group Size:</span>
                  <span className="detail-value">50 Users</span>
                </div>
              </div>

              <div className="progress-container">
                <div className="progress-bar">
                  <div className="progress-fill" style={{ width: '62%' }}></div>
                </div>
                <span className="progress-text">62%</span>
              </div>

              <div className="card-footer">
                <div className="avatars">
                  <div className="avatar-icon"><FaUser /></div>
                  <div className="avatar-icon"><FaUser /></div>
                  <div className="avatar-icon"><FaUser /></div>
                  <span className="avatar-count">+5</span>
                </div>
                <button className="join-button">Join Us</button>
              </div>
            </div>

            {/* Options Strategy Card */}
            <div className="product-card options-card">
              <h3 className="card-title">Time Decay Options Writing Strategy</h3>
              <p className="positions-text">12 Open Positions</p>

              <div className="options-list">
                <div className="option-item">
                  <div className="option-details">
                    <span className="option-exchange">PE</span>
                    <span className="option-name">NIFTY APR 22450 PUT</span>
                  </div>
                  <div className="option-price">
                    <FaArrowRight className="arrow-icon" />
                    <span className="price">₹ 20,000</span>
                  </div>
                </div>

                <div className="option-item">
                  <div className="option-details">
                    <span className="option-exchange">CE</span>
                    <span className="option-name">SHREECEM MAY 26500 CALL</span>
                  </div>
                  <div className="option-price">
                    <FaArrowRight className="arrow-icon" />
                    <span className="price">₹ 45,000</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right side - Text content */}
          <div className="product-info">
            <span className="exclusive-tag">Exclusive Opportunities</span>
            <h2 className="section-title">
              Structured Fin-Products<br />
              not to be found elsewhere
            </h2>
            <p className="section-description">
              Stellar is more than just a SaaS and technology template—it's a
              complete digital transformation solution.
            </p>

            <ul className="feature-list">
              <li className="feature-item">
                <div className="feature-dot"></div>
                <p className="feature-text">
                  Access research and analytics in multiple regional languages
                </p>
              </li>
              <li className="feature-item">
                <div className="feature-dot"></div>
                <p className="feature-text">
                  Gain insights from markets, industries, and diverse asset classes.
                </p>
              </li>
              <li className="feature-item">
                <div className="feature-dot"></div>
                <p className="feature-text">
                  Utilize tools designed for both amateur and professional investors
                </p>
              </li>
              <li className="feature-item">
                <div className="feature-dot"></div>
                <p className="feature-text">
                  Streamlines complex studies into a connected ecosystem
                </p>
              </li>
            </ul>

            <div className="trial-button-container">
              <button className="trial-button">
                Get Free Trial
                <FaArrowRight className="button-arrow" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StructuredProducts;
