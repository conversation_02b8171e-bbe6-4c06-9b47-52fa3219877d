import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import './NewFooter.css';

const NewFooter: React.FC = () => {
  const [email, setEmail] = useState('');

  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Subscribing email:', email);
    setEmail('');
  };

  return (
    <footer className="new-footer">
      {/* Newsletter Section */}
      <div className="container">
        <div className="newsletter-section">
          <div className="row align-items-center">
            <div className="col-lg-5">
              <div className="logo-container">
                <img src="/src/assets/images/Brandwhite.png" alt="Capovex Logo" height="40" />
              </div>
              <p className="newsletter-text">
                Sign up for our weekly non-boring newsletter about money, markets, and more.
              </p>
            </div>
            <div className="col-lg-7">
              <div className="newsletter-form">
                <input
                  type="email"
                  className="form-control"
                  placeholder="Enter your email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
                <button className="btn-subscribe" type="button" onClick={handleSubscribe}>
                  Subscribe
                </button>
              </div>
              <p className="consent-text">
                By providing your email, you are consenting to receive communications from Capovex / Arth.
              </p>
            </div>
          </div>
        </div>

        <hr className="footer-divider" />

        {/* Footer Links Section */}
        <div className="footer-links">
          <div className="row">
            {/* About Us Column */}
            <div className="col-md-6 col-lg">
              <h5 className="footer-heading">About Us</h5>
              <ul className="footer-list">
                <li><Link to="/about">About us</Link></li>
                <li><a href="/vision-values">Vision & Values</a></li>
                <li><a href="/leadership">Leadership & Team</a></li>
                <li><a href="/work-with-us">Work With Us</a></li>
                <li><a href="/impact">Our Impact</a></li>
                <li><a href="/innovations">Innovations</a></li>
              </ul>
            </div>

            {/* Resources Column */}
            <div className="col-md-6 col-lg">
              <h5 className="footer-heading">Resources</h5>
              <ul className="footer-list">
                <li><a href="/arth-shastram">Arth Shastram</a></li>
                <li><a href="/arth-sadhnam">Arth Sadhnam</a></li>
                <li><a href="/arth-rekha">Arth Rekha</a></li>
                <li><a href="/insights">Insights</a></li>
                <li><a href="/submit-article">Submit Article</a></li>
              </ul>
            </div>

            {/* Community Column */}
            <div className="col-md-6 col-lg">
              <h5 className="footer-heading">Community</h5>
              <ul className="footer-list">
                <li><a href="/community-central">Community Central</a></li>
                <li><a href="/knowledge-center">Knowledge Center</a></li>
                <li><a href="/partnership">Partnership</a></li>
                <li><a href="/partnerships">Partnerships</a></li>
                <li><a href="/research">Research</a></li>
                <li><a href="/help-desk">Help Desk</a></li>
                <li><Link to="/scam-alert" onClick={() => console.log('Scam Alert link clicked')}>Scam Alert</Link></li>
              </ul>
            </div>

            {/* Niti Column */}
            <div className="col-md-6 col-lg">
              <h5 className="footer-heading">Niti</h5>
              <ul className="footer-list">
                <li><Link to="/privacy-policy">Privacy Policy</Link></li>
                <li><a href="/terms-of-use">Terms of Use</a></li>
                <li><Link to="/refund-policy">Refund Policy</Link></li>
                <li><a href="/pricing-policy">Pricing Policy</a></li>
                <li><Link to="/cookies-policy">Cookies Policy</Link></li>
                <li><Link to="/abac-policy">ABAC Policy</Link></li>
              </ul>
            </div>

            {/* Contact Us Column */}
            <div className="col-md-6 col-lg">
              <h5 className="footer-heading">Contact Us</h5>
              <div className="contact-info">
                <div className="contact-item">
                  <i className="bi bi-geo-alt-fill"></i>
                  <p>
                    36/4-5, J Block, Golf Course Road Extension, Sector 63, Gurugram, Haryana INDIA 122101
                  </p>
                </div>
                <div className="contact-item">
                  <i className="bi bi-envelope-fill"></i>
                  <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
              </div>

              <h5 className="footer-heading mt-4">Follow us</h5>
              <div className="social-icons-top">
                <a href="https://facebook.com/capovex" className="social-icon-circle" aria-label="Facebook">
                  <i className="bi bi-facebook"></i>
                </a>
                <a href="https://instagram.com/capovex" className="social-icon-circle" aria-label="Instagram">
                  <i className="bi bi-instagram"></i>
                </a>
                <a href="https://linkedin.com/company/capovex" className="social-icon-circle" aria-label="LinkedIn">
                  <i className="bi bi-linkedin"></i>
                </a>
                <a href="https://twitter.com/capovex" className="social-icon-circle" aria-label="Twitter">
                  <i className="bi bi-twitter-x"></i>
                </a>
                <a href="https://youtube.com/capovex" className="social-icon-circle" aria-label="YouTube">
                  <i className="bi bi-youtube"></i>
                </a>
                <a href="https://threads.net/capovex" className="social-icon-circle" aria-label="Threads">
                  <i className="bi bi-threads"></i>
                </a>
              </div>

              <div className="app-download mt-4">
                <a href="https://play.google.com/store/apps/details?id=com.capovex.arth" className="app-button">
                  <div className="button-content">
                    <div className="google-play-logo"></div>
                    <div className="button-text">
                      <span className="small-text">GET IT ON</span>
                      <span className="large-text">Google Play</span>
                    </div>
                  </div>
                </a>
                <a href="https://apps.apple.com/app/capovex-arth/id123456789" className="app-button">
                  <div className="button-content">
                    <div className="apple-logo"></div>
                    <div className="button-text">
                      <span className="small-text">Available on the</span>
                      <span className="large-text">App Store</span>
                    </div>
                  </div>
                </a>
              </div>
            </div>
          </div>
        </div>



        {/* InTech Platform Section */}
        <div className="intech-platform">
          <p>InTech Platform Powered By</p>
          <div className="platform-logos">
            <div className="logo-container">
              <img src="/src/assets/images/artha logo.svg" alt="Artha Logo" />
            </div>
            <div className="logo-container">
              <img src="https://d0.awsstatic.com/logos/powered-by-aws-white.png" alt="AWS Logo" />
            </div>
            <div className="logo-container">
              <img src="/src/assets/images/Krutrim Logo.svg" alt="Krutrim Logo" />
            </div>
            <div className="logo-container">
              <img src="/src/assets/images/NVIDIA Logo.svg" alt="NVIDIA Logo" />
            </div>
          </div>
        </div>

        {/* Copyright Section */}
        <div className="copyright-section">
          <p>© {new Date().getFullYear()} Capovex Research & Analytics Pvt Ltd. All Rights Reserved.</p>
          <p>Proudly Made in India for the world</p>

          <div className="social-icons-bottom">
            <a href="https://facebook.com/capovex" aria-label="Facebook">
              <i className="bi bi-facebook"></i>
            </a>
            <a href="https://youtube.com/capovex" aria-label="YouTube">
              <i className="bi bi-youtube"></i>
            </a>
            <a href="https://twitter.com/capovex" aria-label="Twitter">
              <i className="bi bi-twitter-x"></i>
            </a>
            <a href="https://linkedin.com/company/capovex" aria-label="LinkedIn">
              <i className="bi bi-linkedin"></i>
            </a>
            <a href="https://threads.net/capovex" aria-label="Threads">
              <i className="bi bi-threads"></i>
            </a>
          </div>
        </div>

        {/* Disclosure Section */}
        <div className="disclosure-section">
          <p className="disclosure-title">DISCLOSURE</p>
          <div className="disclosure-content">
            <div className="row">
              <div className="col-md-6">
                <p>
                  Capovex Research & Analytics Pvt. Ltd. is a technology-driven financial platform focused on portfolio
                  execution, risk management, and market intelligence. We have applied for SEBI's Registered
                  Investment Adviser (RIA) and Research Analyst (RA) licenses, which are currently under process. Until
                  these approvals are granted, neither our website nor the Arth app provides stock recommendations,
                  financial planning, or investment advisory services. All tools and insights available within the platform
                  are strictly for informational and educational purposes and should not be construed as investment
                  advice.
                </p>
                <p>
                  Arth operates as a system managed investment execution platform that enables users to monitor,
                  track, and execute trades based on their selected strategies. We strictly adhere to SEBI guidelines and
                  ensure that our platform does not engage in any unauthorized advisory activities. Our platform
                  leverages automated analytics to provide users with financial insights, but all investment decisions
                  remain at the user's sole discretion. Capovex is not a stockbroker, depository participant, or mutual
                  fund distributor, and we do not facilitate direct trading. Instead, we integrate with third-party
                  platforms to enable seamless execution based on user-initiated actions.
                </p>
              </div>
              <div className="col-md-6">
                <p>
                  Our advisory team consists of NISM-certified professionals, CFPs (Certified Financial Technician)
                  and RIAs (Registered Wealth Technicians) with extensive experience and understanding of financial
                  markets expertise. While our team provides educational content and general insights on portfolio strategies,
                  we also offer personalized financial advice through one-on-one sessions with SEBI-registered
                  investment advisors before making any financial decisions. Investing in the stock market carries
                  inherent risks, including potential loss of principal, and past performance cannot guarantee future
                  returns.
                </p>
                <p>
                  Capovex does not provide guaranteed returns, risk-free investments, or assured profits. The use of
                  the Arth app and its services is subject to our Terms & Conditions and Privacy Policy. Users must
                  acknowledge and accept these terms before engaging with the platform. All investment-related
                  decisions are made at the user's discretion, and Capovex is not responsible for any financial losses
                  arising from user actions. We source financial data, corporate actions, and market news from third-
                  party providers, and while we strive for accuracy, we do not guarantee the completeness or reliability
                  of external information.
                </p>
              </div>
            </div>
          </div>
          <p className="disclosure-footer">
            For regulatory or compliance inquiries, users <NAME_EMAIL>. Capovex Research & Analytics Pvt. Ltd. is headquartered at
            J35, 5th Floor, Brahma City J Block, Sector 63, Gurugram, HR, INDIA 122101. © {new Date().getFullYear()} Capovex Research & Analytics Pvt. Ltd. All Rights Reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default NewFooter;
