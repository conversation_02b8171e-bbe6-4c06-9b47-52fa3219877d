.counters-section {
  padding: 5rem 0;
  background-color: #FFFBF7;
  text-align: center;
}

.counters-section .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.section-title {
  font-size: 2.4rem;
  font-weight: 700;
  color: #333;
  margin: 0 auto 4rem;
  text-align: center;
  max-width: 800px;
  position: relative;
  padding-bottom: 1.5rem;
  display: block;
  width: 100%;
}


.counters-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  width: 100%;
  margin-top: 1rem;
}

.counter-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.counter-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.counter-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #FF6B00;
  margin-bottom: 0.75rem;
  margin-top: 0;
  transition: all 0.1s ease-out;
  min-height: 3rem; /* Prevent layout shift during counting */
  display: flex;
  justify-content: center;
  align-items: center;
}

.counter-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  margin-top: 0;
}

.counter-description {
  font-size: 0.9rem;
  line-height: 1.5;
  color: #666;
  margin: 0;
  text-align: center;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .counters-grid {
    gap: 1.5rem;
  }
}

@media (max-width: 992px) {
  .counters-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
  }
}

@media (max-width: 768px) {
  .section-title {
    font-size: 2rem;
    margin-bottom: 3.5rem;
    padding-bottom: 1.2rem;
  }

  .section-title::after {
    width: 60px;
    height: 2.5px;
  }
}

@media (max-width: 576px) {
  .counters-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .section-title {
    font-size: 1.8rem;
    margin-bottom: 3rem;
    padding-bottom: 1rem;
  }

  .section-title::after {
    width: 50px;
    height: 2px;
  }

  .counter-value {
    font-size: 2.2rem;
  }
}
