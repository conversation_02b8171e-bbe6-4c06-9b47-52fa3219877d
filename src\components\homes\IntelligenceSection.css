.intelligence-section {
  padding: 5rem 0;
  background-color: #FFFBF7;
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.intelligence-content {
  display: flex;
  gap: 4rem;
  align-items: flex-start;
}

.intelligence-main {
  flex: 1;
  max-width: 350px;
}

.intelligence-heading {
  font-size: 2.5rem;
  font-weight: 700;
  color: #333;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
}

.intelligence-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #666;
}

.intelligence-features {
  flex: 2;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
}

.intelligence-feature {
  padding-right: 1rem;
}

.feature-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.75rem;
}

.feature-description {
  font-size: 0.9rem;
  line-height: 1.5;
  color: #666;
}

@media (max-width: 992px) {
  .intelligence-content {
    flex-direction: column;
    gap: 3rem;
  }

  .intelligence-main {
    max-width: 100%;
    text-align: center;
  }

  .intelligence-features {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .intelligence-heading {
    font-size: 2rem;
  }

  .intelligence-feature {
    padding-right: 0;
  }
}
