import React from 'react';

interface WireframeSectionProps {
  title: string;
  subtitle: string;
  showCode?: boolean;
  variant: 'primary' | 'secondary' | 'tertiary';
}

const WireframeSection: React.FC<WireframeSectionProps> = ({
  title,
  subtitle,
  showCode = false,
  variant
}) => {
  return (
    <section className={`simple-wireframe-section ${variant}`}>
      <div className="container">
        <div className="row align-items-center">
          <div className={`col-lg-6 ${showCode ? 'order-lg-2' : ''}`}>
            <div className="simple-wireframe-content">
              <h2 className="simple-wireframe-title">{title}</h2>
              <p className="simple-wireframe-description">{subtitle}</p>
            </div>
          </div>
          <div className={`col-lg-6 ${showCode ? 'order-lg-1' : ''}`}>
            <div className="simple-wireframe-visual">
              {showCode ? (
                <div className="simple-code-mockup">
                  <div className="code-window">
                    <div className="code-header">
                      <div className="window-controls">
                        <span className="control red"></span>
                        <span className="control yellow"></span>
                        <span className="control green"></span>
                      </div>
                    </div>
                    <div className="code-body">
                      <div className="code-line"></div>
                      <div className="code-line short"></div>
                      <div className="code-line"></div>
                      <div className="code-line medium"></div>
                      <div className="code-line"></div>
                      <div className="code-line short"></div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="simple-browser-mockup">
                  <div className="browser-header">
                    <div className="browser-controls">
                      <span className="control red"></span>
                      <span className="control yellow"></span>
                      <span className="control green"></span>
                    </div>
                    <div className="address-bar"></div>
                  </div>
                  <div className="browser-content">
                    <div className="content-header"></div>
                    <div className="content-body">
                      <div className="content-line"></div>
                      <div className="content-line short"></div>
                      <div className="content-grid">
                        <div className="grid-item"></div>
                        <div className="grid-item"></div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WireframeSection;
