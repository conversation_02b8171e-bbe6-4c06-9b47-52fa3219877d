// Service layer for scam report API calls
import { ScamReportData } from '../components/forms/ScamReportForm';

export interface ScamReportResponse {
  success: boolean;
  message: string;
  reportId?: string;
}

class ScamReportService {
  private baseUrl = process.env.REACT_APP_API_BASE_URL || '/api';

  async submitScamReport(data: ScamReportData): Promise<ScamReportResponse> {
    try {
      const formData = new FormData();
      
      // Append form fields
      formData.append('scamPlatformLink', data.scamPlatformLink);
      formData.append('advertisementLink', data.advertisementLink);
      formData.append('reportedToCyberCell', data.reportedToCyberCell.toString());
      formData.append('financialLoss', data.financialLoss.toString());
      formData.append('scamDetails', data.scamDetails);

      // Append files if any
      if (data.evidenceFiles) {
        Array.from(data.evidenceFiles).forEach((file, index) => {
          formData.append(`evidenceFile_${index}`, file);
        });
      }

      const response = await fetch(`${this.baseUrl}/scam-reports`, {
        method: 'POST',
        body: formData,
        headers: {
          // Don't set Content-Type for FormData, let browser set it
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Error submitting scam report:', error);
      throw new Error('Failed to submit scam report. Please try again.');
    }
  }

  async getScamReports(page = 1, limit = 10): Promise<any> {
    try {
      const response = await fetch(
        `${this.baseUrl}/scam-reports?page=${page}&limit=${limit}`
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching scam reports:', error);
      throw new Error('Failed to fetch scam reports.');
    }
  }

  async getScamReportById(id: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/scam-reports/${id}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching scam report:', error);
      throw new Error('Failed to fetch scam report.');
    }
  }
}

export const scamReportService = new ScamReportService();
export default scamReportService;
