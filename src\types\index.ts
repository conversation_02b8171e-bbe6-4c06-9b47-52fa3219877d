// Navigation Item Interface
export interface NavigationItem {
  label: string;
  path: string;
  isActive?: boolean;
}

// Feature Card Interface
export interface FeatureCard {
  icon: string;
  title: string;
  text: string;
  link?: string;
}

// Social Media Link Interface
export interface SocialMediaLink {
  platform: string;
  icon: string;
  url: string;
}

// Footer Link Interface
export interface FooterLink {
  label: string;
  url: string;
}

// Form Field Interface
export interface FormField {
  id: string;
  label: string;
  type: string;
  placeholder?: string;
  required?: boolean;
  value: string;
  error?: string;
  touched?: boolean;
}
