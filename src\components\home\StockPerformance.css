.stock-performance-section {
  padding: 5rem 0;
  background-color: #FFFBF7;
  overflow: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.stock-performance-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2.5rem;
  min-height: 500px;
}

/* Left side - Text content */
.performance-info {
  flex: 1;
  max-width: 550px;
}

.dynamic-tag {
  display: inline-block;
  color: #FF6B00;
  font-weight: 500;
  font-size: 0.9rem;
  margin-bottom: 0.75rem;
}

.section-title {
  font-size: 2.2rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
  color: #333;
}

.section-description {
  color: #666;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  position: relative;
}

.feature-check {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #333333;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: bold;
  margin-right: 1rem;
  margin-top: 0.1rem;
  flex-shrink: 0;
}

.feature-text {
  color: #555;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

.trial-button-container {
  margin-top: 1.5rem;
}

.trial-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: transparent;
  border: 1px solid #333;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  font-size: 0.9rem;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

.trial-button:hover {
  background-color: #333;
  color: white;
}

.button-arrow {
  font-size: 0.7rem;
}

/* Right side - Stock image */
.stock-image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 600px;
}

.stock-content-image {
  width: 100%;
  height: auto;
  max-width: 100%;
  object-fit: contain;
}

/* Right side - Stock cards */
.stock-cards-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 380px;
}

.stock-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

/* Stock Details Card */
.details-card {
  padding: 1.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.time-filters {
  display: flex;
  gap: 0.5rem;
}

.time-filter {
  background: none;
  border: none;
  color: #888;
  font-size: 0.8rem;
  cursor: pointer;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.time-filter.active {
  color: #333;
  font-weight: 500;
}

.stock-list {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.stock-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.stock-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stock-symbol {
  font-weight: 600;
  font-size: 0.9rem;
  color: #333;
}

.stock-change {
  font-size: 0.8rem;
  font-weight: 500;
}

.stock-change.positive {
  color: #4CAF50;
}

.stock-change.negative {
  color: #F44336;
}

.stock-price {
  font-weight: 600;
  font-size: 0.9rem;
  color: #333;
}

.stock-chart {
  height: 30px;
  margin-top: 0.5rem;
}

.chart-svg {
  width: 100%;
  height: 100%;
  fill: none;
  stroke-width: 1.5;
  stroke-linecap: round;
  stroke-linejoin: round;
}

.chart-svg.positive {
  stroke: #4CAF50;
}

.chart-svg.negative {
  stroke: #F44336;
}

/* Performance Card */
.performance-card {
  display: flex;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f5f7fa 0%, #f8f9fa 100%);
  position: relative;
  overflow: hidden;
}

.performance-icon {
  width: 60px;
  height: 60px;
  background-color: #f0f0f0;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.5rem;
}

.cloud-icon {
  color: #FF6B00;
  font-size: 1.5rem;
}

.performance-details {
  flex: 1;
}

.performance-header {
  margin-bottom: 1rem;
}

.amount-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.amount {
  font-size: 1.5rem;
  font-weight: 700;
  color: #333;
  margin: 0;
}

.options-button {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  font-size: 0.8rem;
}

.performance-description {
  font-size: 0.8rem;
  color: #666;
  margin: 0;
}

.progress-bars {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.progress-bar {
  flex: 1;
  height: 4px;
  background-color: #e0e0e0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar.gray .progress-fill {
  background-color: #ccc;
}

.progress-bar.orange .progress-fill {
  background-color: #FF6B00;
}

.progress-fill {
  height: 100%;
  border-radius: 2px;
}

.progress-value {
  font-size: 0.8rem;
  font-weight: 500;
  color: #333;
  min-width: 30px;
  text-align: right;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .stock-performance-content {
    gap: 2rem;
  }
}

@media (max-width: 992px) {
  .stock-performance-content {
    flex-direction: column;
    gap: 3rem;
    align-items: center;
  }
  
  .performance-info {
    max-width: 100%;
  }
  
  .stock-cards-container {
    max-width: 450px;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .stock-performance-section {
    padding: 4rem 0;
  }
  
  .section-title {
    font-size: 1.8rem;
  }
}

@media (max-width: 576px) {
  .stock-performance-section {
    padding: 3rem 0;
  }
  
  .section-title {
    font-size: 1.6rem;
  }
}
