.scam-alert-container {
  font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  color: #333;
  line-height: 1.6;
  background-color: #fff;
}

.scam-alert-header {
  background-color: #fff;
  padding: 3rem 0 1.5rem;
  margin-bottom: 2rem;
  position: relative;
}

.scam-alert-header::before {
  content: "";
  position: absolute;
  top: 20px;
  left: 50%;
  width: 6px;
  height: 6px;
  background-color: #ff0000;
  border-radius: 50%;
  transform: translateX(-50%);
}

.scam-alert-header h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #212529;
}

.subtitle {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 0;
}

.scam-alert-content {
  padding: 0 0 5rem;
  max-width: 1200px;
}

.alert-info-section {
  padding-right: 2rem;
}

.intro-text {
  margin-bottom: 2rem;
  text-align: justify;
  font-size: 0.95rem;
  background-color: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.info-section {
  margin-bottom: 2rem;
}

.info-section h2 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #212529;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 0.5rem;
}

.info-section p {
  margin-bottom: 1rem;
  text-align: justify;
  font-size: 0.95rem;
}

.info-section ol {
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.info-section li {
  margin-bottom: 0.5rem;
  text-align: justify;
  font-size: 0.95rem;
}

.report-form-section {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  border: none;
}

.scam-report-form {
  max-width: 100%;
  padding: 0;
}

/* Form Group Styling - Exact Match */
.form-group-exact {
  margin-bottom: 20px;
}

.form-label-exact {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #ff8c42;
  margin-bottom: 8px;
  line-height: 1.4;
}

.form-input-exact {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #ff8c42;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  background-color: #ffffff;
  color: #000000 !important;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input-exact:focus {
  outline: none;
  border-color: #ff6b1a;
  box-shadow: 0 0 0 3px rgba(255, 140, 66, 0.1);
}

.form-input-exact::placeholder {
  color: #ff8c42;
  font-weight: 400;
  opacity: 1;
}

.form-input-exact::-webkit-input-placeholder {
  color: #ff8c42;
  font-weight: 400;
  opacity: 1;
}

.form-input-exact::-moz-placeholder {
  color: #ff8c42;
  font-weight: 400;
  opacity: 1;
}

.form-input-exact:-ms-input-placeholder {
  color: #ff8c42;
  font-weight: 400;
  opacity: 1;
}

.form-input-exact:-moz-placeholder {
  color: #ff8c42;
  font-weight: 400;
  opacity: 1;
}

.form-select-exact {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #ff8c42;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  background-color: #ffffff;
  color: #000000 !important;
  transition: all 0.3s ease;
  box-sizing: border-box;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23ff8c42' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px 16px;
  padding-right: 40px;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.form-select-exact:focus {
  outline: none;
  border-color: #ff6b1a;
  box-shadow: 0 0 0 3px rgba(255, 140, 66, 0.1);
}

.form-select-exact option {
  color: #000000 !important;
  background-color: #ffffff;
}

.form-select-exact option:first-child {
  color: #ff8c42 !important;
}

.form-textarea-exact {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #ff8c42;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  background-color: #ffffff;
  color: #000000 !important;
  transition: all 0.3s ease;
  box-sizing: border-box;
  min-height: 100px;
  resize: vertical;
}

.form-textarea-exact:focus {
  outline: none;
  border-color: #ff6b1a;
  box-shadow: 0 0 0 3px rgba(255, 140, 66, 0.1);
}

.form-textarea-exact::placeholder {
  color: #ff8c42;
  font-weight: 400;
  opacity: 1;
}

.form-textarea-exact::-webkit-input-placeholder {
  color: #ff8c42;
  font-weight: 400;
  opacity: 1;
}

.form-textarea-exact::-moz-placeholder {
  color: #ff8c42;
  font-weight: 400;
  opacity: 1;
}

.form-textarea-exact:-ms-input-placeholder {
  color: #ff8c42;
  font-weight: 400;
  opacity: 1;
}

.form-textarea-exact:-moz-placeholder {
  color: #ff8c42;
  font-weight: 400;
  opacity: 1;
}

.form-help-text {
  font-size: 12px;
  color: #666666;
  margin-top: 6px;
  margin-bottom: 0;
  line-height: 1.4;
}

/* File Upload Styling - Exact Match */
.file-upload-exact {
  margin-top: 8px;
}

.file-upload-area-exact {
  border: 2px dashed #cccccc;
  border-radius: 8px;
  padding: 40px 20px;
  text-align: center;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-upload-area-exact:hover {
  border-color: #ff8c42;
  background-color: #fff9f5;
}

.file-input-hidden {
  display: none;
}

.upload-icon-exact {
  margin-bottom: 15px;
}

.upload-icon-exact i {
  font-size: 40px;
  color: #ff8c42;
  background-color: #fff5f0;
  padding: 15px;
  border-radius: 8px;
  display: inline-block;
}

.upload-main-text {
  color: #333333;
  font-size: 14px;
  margin-bottom: 8px;
  font-weight: 400;
}

.upload-or-text {
  color: #666666;
  font-size: 12px;
  margin: 8px 0;
  font-weight: 400;
}

.browse-files-btn {
  background-color: #ff8c42;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
  margin: 8px 0;
}

.browse-files-btn:hover {
  background-color: #ff6b1a;
}

/* Captcha Styling */
.captcha-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

/* Submit Button Styling - Exact Match */
.submit-button-exact {
  background-color: #333333;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 20px auto 0;
  transition: background-color 0.3s ease;
  min-width: 120px;
}

.submit-button-exact:hover {
  background-color: #555555;
}

.submit-button-exact i {
  font-size: 16px;
}

.file-upload-area {
  position: relative;
  border: 2px dashed #ff8c42;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  background-color: #fff5f0;
  transition: border-color 0.3s ease;
}

.file-upload-area:hover {
  border-color: #ff6b1a;
}

.file-input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  cursor: pointer;
}

.file-upload-text {
  pointer-events: none;
}

.file-upload-text i {
  font-size: 2rem;
  color: #ff8c42;
  margin-bottom: 1rem;
  display: block;
}

.file-upload-text p {
  margin-bottom: 0.5rem;
  color: #ff8c42;
  font-size: 0.9rem;
}

.file-info {
  color: #ff8c42 !important;
  font-size: 0.8rem !important;
}

.browse-btn {
  background-color: #ff8c42;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  margin: 0.5rem 0;
  pointer-events: all;
  transition: background-color 0.3s ease;
}

.browse-btn:hover {
  background-color: #ff6b1a;
}

.file-types {
  color: #ff8c42 !important;
  font-size: 0.8rem !important;
  margin-top: 0.5rem !important;
}

.captcha-section {
  margin: 1.5rem 0;
  display: flex;
  justify-content: center;
}

.submit-btn {
  background-color: #333;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 1.5rem auto 0;
  transition: background-color 0.3s ease;
}

.submit-btn:hover {
  background-color: #555;
}

.submit-btn i {
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .scam-alert-content {
    padding: 0 1rem 5rem;
  }
}

@media (max-width: 992px) {
  .scam-alert-header h1 {
    font-size: 2.5rem;
  }

  .alert-info-section {
    padding-right: 0;
    margin-bottom: 3rem;
  }

  .scam-alert-content {
    padding: 0 1rem 4rem;
  }
}

@media (max-width: 768px) {
  .scam-alert-header {
    padding: 2rem 0 1.5rem;
  }

  .scam-alert-header h1 {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .scam-alert-content {
    padding: 0 0.5rem 3rem;
  }

  .intro-text {
    padding: 1rem;
  }

  .report-form-section {
    padding: 1.5rem;
  }

  .info-section h2 {
    font-size: 1.2rem;
  }

  .info-section p,
  .info-section li {
    font-size: 0.9rem;
  }

  .file-upload-area {
    padding: 1.5rem;
  }

  .file-upload-text i {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .scam-alert-header {
    padding: 1.5rem 0 1rem;
  }

  .scam-alert-header h1 {
    font-size: 1.75rem;
  }

  .scam-alert-content {
    padding: 0 0.25rem 2.5rem;
  }

  .intro-text {
    padding: 0.75rem;
  }

  .report-form-section {
    padding: 1rem;
  }

  .info-section h2 {
    font-size: 1.1rem;
  }

  .info-section p,
  .info-section li {
    font-size: 0.85rem;
  }

  .file-upload-area {
    padding: 1rem;
  }

  .submit-btn {
    padding: 0.6rem 1.5rem;
    font-size: 0.9rem;
  }
}

/* Force placeholder color with highest specificity */
.scam-alert-container input::placeholder,
.scam-alert-container textarea::placeholder,
.scam-alert-container select::placeholder {
  color: #ff8c42 !important;
  opacity: 1 !important;
}

.scam-alert-container input::-webkit-input-placeholder,
.scam-alert-container textarea::-webkit-input-placeholder,
.scam-alert-container select::-webkit-input-placeholder {
  color: #ff8c42 !important;
  opacity: 1 !important;
}

.scam-alert-container input::-moz-placeholder,
.scam-alert-container textarea::-moz-placeholder,
.scam-alert-container select::-moz-placeholder {
  color: #ff8c42 !important;
  opacity: 1 !important;
}

.scam-alert-container input:-ms-input-placeholder,
.scam-alert-container textarea:-ms-input-placeholder,
.scam-alert-container select:-ms-input-placeholder {
  color: #ff8c42 !important;
  opacity: 1 !important;
}

.scam-alert-container input:-moz-placeholder,
.scam-alert-container textarea:-moz-placeholder,
.scam-alert-container select:-moz-placeholder {
  color: #ff8c42 !important;
  opacity: 1 !important;
}
