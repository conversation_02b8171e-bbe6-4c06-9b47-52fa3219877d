// Suggested refactored form component
import React from 'react';
import { useScamReportForm } from '../../hooks/useScamReportForm';
import FormField from './FormField';
import FileUpload from './FileUpload';
import './ScamReportForm.css';

interface ScamReportFormProps {
  onSubmit: (data: ScamReportData) => Promise<void>;
  className?: string;
}

export interface ScamReportData {
  scamPlatformLink: string;
  advertisementLink: string;
  reportedToCyberCell: boolean;
  financialLoss: boolean;
  scamDetails: string;
  evidenceFiles: FileList | null;
}

const ScamReportForm: React.FC<ScamReportFormProps> = ({ onSubmit, className }) => {
  const {
    formData,
    errors,
    isSubmitting,
    handleInputChange,
    handleFileChange,
    handleSubmit,
    resetForm
  } = useScamReportForm({ onSubmit });

  return (
    <form onSubmit={handleSubmit} className={`scam-report-form ${className || ''}`}>
      <FormField
        label="Link to Scam Platform*"
        name="scamPlatformLink"
        value={formData.scamPlatformLink}
        onChange={handleInputChange}
        error={errors.scamPlatformLink}
        placeholder="Enter platform link"
        helpText="Enter Link of Website, Telegram Channel or Whatsapp Group"
        required
      />

      <FormField
        label="Link to Scam Advertisements*"
        name="advertisementLink"
        value={formData.advertisementLink}
        onChange={handleInputChange}
        error={errors.advertisementLink}
        placeholder="Enter advertisement link"
        helpText="Is the Scammer Advertising? Share the Link of Advertisement."
        required
      />

      <FormField
        label="Have you Reported the Scammer to Cyber Cell / Police*"
        name="reportedToCyberCell"
        type="select"
        value={formData.reportedToCyberCell}
        onChange={handleInputChange}
        error={errors.reportedToCyberCell}
        options={[
          { value: '', label: 'Have you Reported the Scammer to Cyber Cell / Police' },
          { value: 'yes', label: 'Yes' },
          { value: 'no', label: 'No' }
        ]}
        required
      />

      <FormField
        label="Did You suffer any financial losses*"
        name="financialLoss"
        type="select"
        value={formData.financialLoss}
        onChange={handleInputChange}
        error={errors.financialLoss}
        options={[
          { value: '', label: 'Did You suffer any financial losses' },
          { value: 'yes', label: 'Yes' },
          { value: 'no', label: 'No' }
        ]}
        required
      />

      <FormField
        label="Details of the Scam and how did you find it is a scam*"
        name="scamDetails"
        type="textarea"
        value={formData.scamDetails}
        onChange={handleInputChange}
        error={errors.scamDetails}
        placeholder="Describe the scam details"
        helpText="This is the description area"
        rows={4}
        required
      />

      <FileUpload
        label="Upload Evidences"
        name="evidenceFiles"
        onChange={handleFileChange}
        error={errors.evidenceFiles}
        helpText="You can upload up to 5 files max"
        acceptedTypes=".jpg,.jpeg,.png,.pdf,.doc,.docx"
        maxFiles={5}
      />

      <div className="form-actions">
        <button 
          type="submit" 
          className="submit-button-exact"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Sending...' : 'Send'} 
          <i className="bi bi-arrow-right"></i>
        </button>
      </div>
    </form>
  );
};

export default ScamReportForm;
