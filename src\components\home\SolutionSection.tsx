import React from 'react';
import './SolutionSection.css';
import { FaFire, FaBolt, FaChartPie, FaBell } from 'react-icons/fa';
import mobileImage from '../../assets/images/Mobile.png';

const SolutionSection: React.FC = () => {
  return (
    <section className="solution-section">
      <div className="container">
        <div className="solution-content">
          {/* Left side - Text content and features */}
          <div className="solution-text">
            <div className="solution-header">
              <span className="features-tag">Our Key Features</span>
              <h2 className="section-title">
                Build a solution that wins
                you more customers.
              </h2>
            </div>

            <div className="features-grid">
              <div className="feature-item">
                <div className="feature-icon-container">
                  <FaFire className="feature-icon" />
                </div>
                <h3 className="feature-title">Deploy faster together</h3>
                <p className="feature-description">
                  Gain a competitive edge with our SEO optimization tools
                </p>
              </div>

              <div className="feature-item">
                <div className="feature-icon-container">
                  <FaBolt className="feature-icon" />
                </div>
                <h3 className="feature-title">Beautiful No-Code</h3>
                <p className="feature-description">
                  Enhance your website's visibility and drive targeted traffic
                </p>
              </div>

              <div className="feature-item">
                <div className="feature-icon-container">
                  <FaChartPie className="feature-icon" />
                </div>
                <h3 className="feature-title">Good Communication</h3>
                <p className="feature-description">
                  Experience the Stellar difference and unlock the true potential
                </p>
              </div>

              <div className="feature-item">
                <div className="feature-icon-container">
                  <FaBell className="feature-icon" />
                </div>
                <h3 className="feature-title">Easily Customised</h3>
                <p className="feature-description">
                  From content creation and deployment to performance
                </p>
              </div>
            </div>
          </div>

          {/* Right side - Mobile image */}
          <div className="mobile-showcase">
            <img src={mobileImage} alt="Mobile app interface" className="mobile-image" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default SolutionSection;
