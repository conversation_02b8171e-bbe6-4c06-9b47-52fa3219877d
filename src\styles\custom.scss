// Custom Bootstrap Variables
$primary: #0d6efd;
$secondary: #ff6b35;
$gray-800: #343a40;
$white: #ffffff;

// Custom CSS Variables
:root {
  --primary: #{$primary};
  --secondary: #{$secondary};
  --gray-800: #{$gray-800};
  --white: #{$white};
  --section-spacing: 2rem;
  --card-spacing: 1.5rem;
}

// Import Bootstrap
@import "bootstrap/scss/bootstrap";

// Custom Styles
body {
  font-family: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

// Typography
h1, .h1 {
  font-size: 3rem;
  font-weight: 700;
}

h2, .h2 {
  font-size: 2rem;
  font-weight: 600;
}

// Spacing - Commented out to prevent conflicts with individual section styles
// section {
//   margin-bottom: var(--section-spacing);
// }

// Navbar
.navbar {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  
  .navbar-brand {
    font-weight: 700;
  }
  
  .nav-link {
    font-weight: 500;
    
    &.active {
      color: var(--secondary) !important;
    }
  }
}

// Hero Section - Commented out to prevent conflicts with individual hero components
// .hero-section {
//   position: relative;
//   background-size: cover;
//   background-position: center;
//   color: var(--white);
//   padding: 5rem 0;
//
//   &::before {
//     content: '';
//     position: absolute;
//     top: 0;
//     left: 0;
//     width: 100%;
//     height: 100%;
//     background-color: rgba(0, 0, 0, 0.5);
//     z-index: 1;
//   }
//
//   .container {
//     position: relative;
//     z-index: 2;
//   }
//
//   .btn-cta {
//     background-color: var(--secondary);
//     border-color: var(--secondary);
//
//     &:hover {
//       background-color: darken($secondary, 10%);
//       border-color: darken($secondary, 10%);
//     }
//   }
// }

// Feature Cards
.feature-card {
  height: 100%;
  transition: transform 0.3s, box-shadow 0.3s;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
  
  .card-icon {
    font-size: 2.5rem;
    color: var(--secondary);
    margin-bottom: 1rem;
  }
}

// Sidebar
.sidebar {
  @include media-breakpoint-up(lg) {
    position: sticky;
    top: 2rem;
  }
}

// Footer
.footer {
  background-color: var(--gray-800);
  color: var(--white);
  padding: 3rem 0 0;
  
  h5 {
    color: var(--secondary);
    margin-bottom: 1.5rem;
  }
  
  a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    
    &:hover {
      color: var(--white);
    }
  }
  
  .social-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
  }
  
  .copyright {
    background-color: rgba(0, 0, 0, 0.2);
    padding: 1rem 0;
    margin-top: 3rem;
  }
}

// Forms
.form-control:focus {
  border-color: var(--secondary);
  box-shadow: 0 0 0 0.25rem rgba($secondary, 0.25);
}

.btn-primary {
  background-color: var(--primary);
  border-color: var(--primary);
  
  &:hover {
    background-color: darken($primary, 10%);
    border-color: darken($primary, 10%);
  }
}

.btn-secondary {
  background-color: var(--secondary);
  border-color: var(--secondary);
  
  &:hover {
    background-color: darken($secondary, 10%);
    border-color: darken($secondary, 10%);
  }
}

// Animations
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
  20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.shake {
  animation: shake 0.5s;
}

// Accessibility
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

// Responsive Images
.img-fluid {
  max-width: 100%;
  height: auto;
}
