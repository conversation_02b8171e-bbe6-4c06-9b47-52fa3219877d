// Reusable form field component
import React from 'react';
import './FormField.css';

interface Option {
  value: string;
  label: string;
}

interface FormFieldProps {
  label: string;
  name: string;
  type?: 'text' | 'email' | 'url' | 'textarea' | 'select';
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  onBlur?: (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  error?: string;
  placeholder?: string;
  helpText?: string;
  required?: boolean;
  disabled?: boolean;
  rows?: number;
  options?: Option[];
  className?: string;
}

const FormField: React.FC<FormFieldProps> = ({
  label,
  name,
  type = 'text',
  value,
  onChange,
  onBlur,
  error,
  placeholder,
  helpText,
  required = false,
  disabled = false,
  rows = 3,
  options = [],
  className = ''
}) => {
  const baseClassName = `form-group-exact ${className}`;
  const hasError = !!error;

  const renderInput = () => {
    const commonProps = {
      id: name,
      name,
      value,
      onChange,
      onBlur,
      disabled,
      required,
      className: `form-input-exact ${hasError ? 'error' : ''}`,
      placeholder,
      'aria-describedby': helpText ? `${name}-help` : undefined,
      'aria-invalid': hasError
    };

    switch (type) {
      case 'textarea':
        return (
          <textarea
            {...commonProps}
            className={`form-textarea-exact ${hasError ? 'error' : ''}`}
            rows={rows}
          />
        );

      case 'select':
        return (
          <select
            {...commonProps}
            className={`form-select-exact ${hasError ? 'error' : ''}`}
          >
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      default:
        return (
          <input
            {...commonProps}
            type={type}
          />
        );
    }
  };

  return (
    <div className={baseClassName}>
      <label htmlFor={name} className="form-label-exact">
        {label}
        {required && <span className="required-indicator">*</span>}
      </label>
      
      {renderInput()}
      
      {error && (
        <div className="form-error" role="alert" aria-live="polite">
          {error}
        </div>
      )}
      
      {helpText && (
        <p id={`${name}-help`} className="form-help-text">
          {helpText}
        </p>
      )}
    </div>
  );
};

export default FormField;
