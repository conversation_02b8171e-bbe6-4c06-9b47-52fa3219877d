.features-section {
  padding: 5rem 0;
  background-color: #FFFBF7;
  position: relative;
  z-index: 1;
  isolation: isolate;
}

.container {
  max-width: 1140px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

.features-header {
  text-align: center;
  margin-bottom: 3.5rem;
}

.features-title {
  font-size: 2.25rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.features-subtitle {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* New flexbox-based layout */
.features-container {
  max-width: 1080px;
  margin: 0 auto;
  padding: 0 15px;
}

.features-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.features-row:last-child {
  margin-bottom: 0;
}

.feature-col {
  flex: 0 0 calc(33.333% - 1.25rem);
  max-width: calc(33.333% - 1.25rem);
}

.feature-card {
  background-color: #fff;
  border-radius: 10px;
  padding: 2.25rem 1.75rem;
  text-align: center;
  border: 1px solid rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 220px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
  width: 100%;
}

.feature-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  border-color: rgba(0, 0, 0, 0.3);
}

.feature-icon {
  margin-bottom: 1.5rem;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon img {
  width: 28px;
  height: 28px;
  filter: invert(56%) sepia(75%) saturate(1582%) hue-rotate(346deg) brightness(101%) contrast(101%);
}

.feature-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #FF7A45;
  margin-bottom: 0.85rem;
}

.feature-description {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
  max-width: 260px;
  margin: 0 auto;
}

@media (max-width: 992px) {
  .features-title {
    font-size: 2rem;
  }

  .feature-col {
    flex: 0 0 calc(33.333% - 1rem);
    max-width: calc(33.333% - 1rem);
  }

  .features-row {
    margin-bottom: 2rem;
  }

  .feature-card {
    padding: 1.75rem 1.25rem;
  }
}

@media (max-width: 768px) {
  .features-section {
    padding: 4rem 0;
  }

  .features-header {
    margin-bottom: 2.5rem;
  }

  .features-title {
    font-size: 1.75rem;
  }

  .features-subtitle {
    font-size: 0.95rem;
  }

  .features-container {
    padding: 0 10px;
  }

  .features-row {
    margin-bottom: 1.5rem;
  }

  .feature-col {
    flex: 0 0 calc(33.333% - 0.75rem);
    max-width: calc(33.333% - 0.75rem);
  }

  .feature-icon img {
    width: 24px;
    height: 24px;
  }

  .feature-description {
    font-size: 0.85rem;
    max-width: 220px;
  }

  .feature-card {
    padding: 1.75rem 1.25rem;
    min-height: 200px;
  }
}

@media (max-width: 576px) {
  .features-section {
    padding: 3rem 0;
  }

  .features-title {
    font-size: 1.5rem;
  }

  .features-subtitle br {
    display: none;
  }

  .features-container {
    overflow-x: auto;
    padding: 0;
  }

  .features-row {
    min-width: 480px;
    margin-bottom: 1rem;
    padding: 0 1rem;
  }

  .feature-col {
    flex: 0 0 calc(33.333% - 0.5rem);
    max-width: calc(33.333% - 0.5rem);
  }

  .feature-card {
    min-height: 170px;
    padding: 1.25rem 0.75rem;
    border: 1px solid rgba(0, 0, 0, 0.2);
  }

  .feature-description {
    font-size: 0.75rem;
    max-width: 100%;
  }

  .feature-title {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .feature-icon {
    margin-bottom: 0.75rem;
  }

  .feature-icon img {
    width: 20px;
    height: 20px;
  }
}

/* Container for horizontal scrolling on very small screens */
@media (max-width: 480px) {
  .container {
    padding: 0;
  }

  .features-container {
    min-width: 450px;
  }

  .feature-card {
    min-height: 160px;
    border: 1px solid rgba(0, 0, 0, 0.2);
  }
}
