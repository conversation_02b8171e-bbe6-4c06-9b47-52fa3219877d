import React, { useState, useEffect, useRef } from 'react';
import './LanguageCarousel.css';

// Import language images
import hindiImage from '../../assets/images/hindi.png';
import gujaratiImage from '../../assets/images/gujrati.png';
import tamilImage from '../../assets/images/tamil.png';
import marathiImage from '../../assets/images/marathi.png';
import englishImage from '../../assets/images/english.png';

// Define the language data structure
interface LanguageData {
  id: number;
  image: string;
}

const LanguageCarousel: React.FC = () => {
  // Sample language data - duplicate items to create a continuous train effect
  const cards: LanguageData[] = [
    { id: 1, image: hindiImage },
    { id: 2, image: gujaratiImage },
    { id: 3, image: tamilImage },
    { id: 4, image: marathiImage },
    { id: 5, image: englishImage },
    // Duplicate the cards to create a continuous train
    { id: 6, image: hindiImage },
    { id: 7, image: gujaratiImage },
    { id: 8, image: tamilImage },
    { id: 9, image: marathiImage },
    { id: 10, image: englishImage }
  ];

  // State to track the position of the train
  const [position, setPosition] = useState(50); // Start with a slight offset
  const animationRef = useRef<number>(0);
  const carouselRef = useRef<HTMLDivElement>(null);

  // Set up continuous train-like movement
  useEffect(() => {
    const speed = 1; // pixels per frame - adjust for desired speed
    const cardWidth = 310; // width of each card including gap (280px + 30px margin)
    const totalWidth = cardWidth * 5; // width of the original set of cards

    const animate = () => {
      setPosition(prevPosition => {
        const newPosition = prevPosition - speed;

        // Reset position when we've moved one full set of cards
        if (newPosition <= -totalWidth) {
          return newPosition + totalWidth;
        }

        // Move the train left continuously
        return newPosition;
      });

      animationRef.current = requestAnimationFrame(animate);
    };

    // Start the animation immediately
    animationRef.current = requestAnimationFrame(animate);

    // Clean up animation on component unmount
    return () => {
      cancelAnimationFrame(animationRef.current);
    };
  }, []);

  return (
    <section className="language-carousel-section">
      <div className="container">
        <div className="language-header text-center">
          <h2 className="language-title">Multilingual Financial<br />Intelligence</h2>
          <p className="language-subtitle">
            Unlock a World of Powerful Insights, curated analysis<br />
            and tools , All in Your Preferred Language
          </p>
        </div>

        <div className="carousel-container">
          {/* Language Cards Train */}
          <div className="language-train-container" ref={carouselRef}>
            <div
              className="language-train"
              style={{ transform: `translateX(${position}px)` }}
            >
              {cards.map((card) => (
                <div
                  key={card.id}
                  className="language-card-train"
                >
                  <img
                    src={card.image}
                    alt={`Language ${card.id}`}
                    className="language-image-full"
                    loading="lazy"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LanguageCarousel;
