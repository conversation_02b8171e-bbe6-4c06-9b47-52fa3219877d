import React from "react";
import { Container, Table } from "react-bootstrap";
import "bootstrap/dist/css/bootstrap.min.css";
import './KeyManagement.css';
type KeyManagementPerson = {
  name: string;
  designation: string;
  phone: string;
  email: string;
};

const keyManagementData: KeyManagementPerson[] = [
  {
    name: "<PERSON><PERSON><PERSON>",
    designation: "Managing Director",
    phone: "0755-4311100 Ext-21",
    email: "<EMAIL>",
  },
  {
    name: "<PERSON><PERSON><PERSON> A<PERSON>wal",
    designation: "Chief of Technology & Innovation",
    phone: "0755-4311100 Ext-40",
    email: "<EMAIL>",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    designation: "Chief of Product & Marketing",
    phone: "0755-4311100 Ext-15",
    email: "<EMAIL>",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    designation: "Chief of Operations",
    phone: "0755-4311100 Ext-38",
    email: "sing<PERSON>.<EMAIL>",
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    designation: "Chief of Compliances",
    phone: "0755-4311100 Ext-22",
    email: "<EMAIL>",
  },
  {
    name: "Tushar Suryavanshi",
    designation: "Head of Compliances",
    phone: "0755-4311100 Ext-34",
    email: "<EMAIL>",
  },
];

const KeyManagementTable: React.FC = () => {
  return (
    <Container className="my-5 pb-5">
      <h3 className="fw-bold mb-4">Key Management Personal</h3>
      <Table  className="custom-table" style={{ borderBottom: "none" }}>
        <thead>
          <tr>
            <th className="text-orange fw-semibold">Key Management</th>
            <th className="text-orange fw-semibold">Designation</th>
            <th className="text-orange fw-semibold">Phone (Ext)</th>
            <th className="text-orange fw-semibold">Email ID</th>
          </tr>
        </thead>
        <tbody>
          {keyManagementData.map((person, idx) => (
            <tr key={idx}>
              <td>{person.name}</td>
              <td>{person.designation}</td>
              <td>{person.phone}</td>
              <td>
               <a href={`mailto:${person.email}`} className="text-decoration-none" style={{ color: "black" }}>
                  {person.email}
                </a>
              </td>
            </tr>
          ))}
        </tbody>
      </Table>
      
     
    </Container>
  );
};

export default KeyManagementTable;
