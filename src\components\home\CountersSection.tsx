import React, { useState, useEffect, useRef } from 'react';
import './CountersSection.css';

// Define counter data structure
interface CounterData {
  id: number;
  finalValue: number;
  suffix: string;
  title: string;
  description: string;
}

const CountersSection: React.FC = () => {
  // Define counter data
  const countersData: CounterData[] = [
    {
      id: 1,
      finalValue: 8,
      suffix: '+',
      title: 'Advanced Indicators',
      description: 'Our platform features over eight meticulously coded indicators, providing deep insights to'
    },
    {
      id: 2,
      finalValue: 84,
      suffix: '%',
      title: 'Analytica Accuracy',
      description: 'We take pride in our high customer satisfaction rate, reflecting our commitment to'
    },
    {
      id: 3,
      finalValue: 250,
      suffix: '+',
      title: 'Curated Reports',
      description: 'Access a vast library of over 200 expertly curated reports, designed to keep you informed about the'
    },
    {
      id: 4,
      finalValue: 400,
      suffix: '+',
      title: 'Beta Testers',
      description: 'Join over 400 beta testers who are already benefiting from our cutting-edge market analysis tools.'
    }
  ];

  // State to track current counter values
  const [counterValues, setCounterValues] = useState<number[]>(countersData.map(() => 0));

  // Ref to track if counters have started
  const countersStarted = useRef(false);

  // Ref for section element to detect when it's in viewport
  const sectionRef = useRef<HTMLElement>(null);

  // Function to check if element is in viewport
  const isInViewport = (element: HTMLElement): boolean => {
    const rect = element.getBoundingClientRect();
    return (
      rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
      rect.bottom >= 0
    );
  };

  // Function to start counter animation
  const startCounters = () => {
    if (countersStarted.current) return;
    countersStarted.current = true;

    // Animation duration in milliseconds
    const duration = 2000;
    // Update interval in milliseconds
    const interval = 20;
    // Number of steps
    const steps = duration / interval;

    // Current step
    let currentStep = 0;

    const timer = setInterval(() => {
      currentStep++;

      if (currentStep >= steps) {
        // Animation complete, set final values
        setCounterValues(countersData.map(counter => counter.finalValue));
        clearInterval(timer);
      } else {
        // Calculate intermediate values based on easeOutQuad function
        setCounterValues(countersData.map(counter => {
          const progress = currentStep / steps;
          // Using easeOutQuad for smoother animation
          const easeProgress = 1 - (1 - progress) * (1 - progress);
          return Math.floor(counter.finalValue * easeProgress);
        }));
      }
    }, interval);
  };

  // Set up scroll event listener
  useEffect(() => {
    const handleScroll = () => {
      if (sectionRef.current && isInViewport(sectionRef.current)) {
        startCounters();
      }
    };

    // Check on mount and add scroll listener
    handleScroll();
    window.addEventListener('scroll', handleScroll);

    // Clean up
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <section className="counters-section" ref={sectionRef}>
      <div className="container">
        <h2 className="section-title">Few Counters at a Glance</h2>

        <div className="counters-grid">
          {countersData.map((counter, index) => (
            <div className="counter-item" key={counter.id}>
              <h3 className="counter-value">
                {counterValues[index]}{counter.suffix}
              </h3>
              <h4 className="counter-title">{counter.title}</h4>
              <p className="counter-description">
                {counter.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CountersSection;
