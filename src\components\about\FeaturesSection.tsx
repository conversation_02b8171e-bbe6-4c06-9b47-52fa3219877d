import React from 'react';

const AboutFeaturesSection: React.FC = () => {
  return (
    <section className="about-features-section">
      <div className="about-features-container">
        <h2 className="about-features-title">
        A modular system of brains, hearts, & habits, Engineered for the Future
        </h2>

        <div className="about-features-grid">
          <div className="about-feature-card">
            <div className="about-feature-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <polyline points="7,10 12,15 17,10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h3 className="about-feature-title">Core Intelligence</h3>
            <p className="about-feature-description">
            ML-powered strategy engines built for reasoning, volatility modeling, and investment execution
            </p>
          </div>

          <div className="about-feature-card">
            <div className="about-feature-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <polyline points="7,10 12,15 17,10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h3 className="about-feature-title">Heart Layer</h3>
            <p className="about-feature-description">
            Emotional intelligence layer with memory, preferences, and contextual personality for human-like interactions
            </p>
          </div>

          <div className="about-feature-card">
            <div className="about-feature-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <polyline points="7,10 12,15 17,10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                <line x1="12" y1="15" x2="12" y2="3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </div>
            <h3 className="about-feature-title">Persona Engine</h3>
            <p className="about-feature-description">
            Turn your financial habits into meaningful investment with adaptive persona based learning engine
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutFeaturesSection;
