import React from 'react';
import Hero from '../components/home/<USER>';
import FeaturesSection from '../components/home/<USER>';
// // import FeaturesSection from '../components/homes/FeaturesSection';
import LanguageCarousel from '../components/home/<USER>';
import ContentSection from '../components/home/<USER>';
// import Features from '../components/homes/Features';
import StockPerformance from '../components/home/<USER>';
import CountersSection from '../components/home/<USER>';
import SolutionSection from '../components/home/<USER>';
import IntelligenceSection from '../components/home/<USER>';
import CtaSection from '../components/home/<USER>';
import TestimonialSection from '../components/home/<USER>';

const Home: React.FC = () => {
  return (
    <main>
      {/* Hero Section */}
      <Hero />

      {/* Features Section */}
      <FeaturesSection />

      {/* Language Carousel */}
      <LanguageCarousel />

      {/* Content Section  */}
      <ContentSection />

      {/* Stock Performance Content Section  */}
      <StockPerformance />

      {/* Counters Section  */}
      <CountersSection />

      {/* Solution Section  */}
      <SolutionSection />

      {/* Intelligence Section  */}
      <IntelligenceSection />

      {/* Cta Section  */}
      <CtaSection />

      {/* Testimonial Section  */}
      <TestimonialSection />  


    </main>
  );
};

export default Home;
