.content-section {
  padding: 5rem 0;
  background-color: #faf9f7;
  overflow: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.content-section-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4rem;
}

/* Left side - Image */
.content-image-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.content-part-image {
  max-width: 100%;
  height: auto;
  object-fit: contain;
}

/* Right side - Text content */
.content-info {
  flex: 1;
  max-width: 550px;
}

.exclusive-tag {
  display: inline-block;
  color: #FF6B00;
  font-weight: 700;
  font-size: 0.9rem;
  margin-bottom: 0.75rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  color: #111;
}

.section-description {
  color: #444;
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

.feature-list {
  list-style: none;
  padding: 0;
  margin-bottom: 2rem;
}

.content-section .feature-item {
  display: flex !important;
  align-items: flex-start !important; /* Align to top instead of center */
  margin-bottom: 1.25rem !important;
  position: relative !important;
}

.content-section .feature-check-circle {
  width: 18px !important;
  height: 18px !important;
  border-radius: 50% !important;
  background-color: #333 !important;
  margin-right: 0.75rem !important;
  flex-shrink: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.content-section .check-icon {
  color: white !important;
  font-size: 0.65rem !important;
}

.content-section .feature-text {
  color: #555 !important;
  font-size: 0.95rem !important;
  line-height: 1.4 !important;
  margin: 0 !important;
}

.trial-button-container {
  margin-top: 2rem;
}

.trial-button {
  display: flex;
  align-items: center;
  background-color: white;
  border: none;
  border-radius: 30px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  font-size: 0.9rem;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  width: fit-content;
}

.trial-button:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.arrow-icon {
  margin-left: 0.75rem;
  font-size: 0.8rem;
}

.trial-button:hover .arrow-icon {
  transform: translateX(3px);
  transition: transform 0.2s ease;
}

/* Responsive styles */
@media (max-width: 1200px) {
  .content-section-wrapper {
    gap: 3rem;
  }
}

@media (max-width: 992px) {
  .content-section-wrapper {
    flex-direction: column;
    gap: 3rem;
    align-items: center;
  }

  .content-info {
    max-width: 100%;
    order: 1;
  }

  .content-image-container {
    order: 2;
    width: 100%;
    max-width: 500px;
  }
}

@media (max-width: 768px) {
  .content-section {
    padding: 4rem 0;
  }

  .section-title {
    font-size: 1.8rem;
  }
}

@media (max-width: 576px) {
  .content-section {
    padding: 3rem 0;
  }

  .section-title {
    font-size: 1.6rem;
  }
}
