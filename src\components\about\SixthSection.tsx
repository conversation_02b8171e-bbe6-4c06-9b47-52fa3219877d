import React from 'react';

const SixthSection: React.FC = () => {
  return (
    <section className="sixth-section">
      <div className="sixth-section-container">
        <div className="sixth-section-header">
          <h2 className="sixth-title">Why <PERSON><PERSON> Feels Like Magic</h2>
          <p className="sixth-subtitle">
          A modular system of brains, hearts, and habits — <PERSON><PERSON>’s architecture mimics human cognition and emotion, but scales like AI.
          </p>
        </div>
        
        <div className="sixth-cards-grid">
          <div className="sixth-card">
            <div className="sixth-card-image">
              <svg width="60" height="60" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" stroke="#D1D5DB" strokeWidth="2" fill="none"/>
                <circle cx="8.5" cy="8.5" r="1.5" fill="#D1D5DB"/>
                <path d="M21 15l-5-5L5 21" stroke="#D1D5DB" strokeWidth="2"/>
              </svg>
            </div>
            <div className="sixth-card-content">
              <h3 className="sixth-card-title">
              Personalised Portfolios
              </h3>
              <p className="sixth-card-description">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliq
              </p>
            </div>
          </div>

          <div className="sixth-card">
            <div className="sixth-card-image">
              <svg width="60" height="60" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" stroke="#D1D5DB" strokeWidth="2" fill="none"/>
                <circle cx="8.5" cy="8.5" r="1.5" fill="#D1D5DB"/>
                <path d="M21 15l-5-5L5 21" stroke="#D1D5DB" strokeWidth="2"/>
              </svg>
            </div>
            <div className="sixth-card-content">
              <h3 className="sixth-card-title">
              Behavior-First Risk Engine              </h3>
              <p className="sixth-card-description">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliq
              </p>
            </div>
          </div>

          <div className="sixth-card">
            <div className="sixth-card-image">
              <svg width="60" height="60" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" stroke="#D1D5DB" strokeWidth="2" fill="none"/>
                <circle cx="8.5" cy="8.5" r="1.5" fill="#D1D5DB"/>
                <path d="M21 15l-5-5L5 21" stroke="#D1D5DB" strokeWidth="2"/>
              </svg>
            </div>
            <div className="sixth-card-content">
              <h3 className="sixth-card-title">
              24 hour available analysts              </h3>
              <p className="sixth-card-description">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliq
              </p>
            </div>
          </div>

          <div className="sixth-card">
            <div className="sixth-card-image">
              <svg width="60" height="60" viewBox="0 0 24 24" fill="none">
                <rect x="3" y="3" width="18" height="18" rx="2" stroke="#D1D5DB" strokeWidth="2" fill="none"/>
                <circle cx="8.5" cy="8.5" r="1.5" fill="#D1D5DB"/>
                <path d="M21 15l-5-5L5 21" stroke="#D1D5DB" strokeWidth="2"/>
              </svg>
            </div>
            <div className="sixth-card-content">
              <h3 className="sixth-card-title">
              User Experience like never before              </h3>
              <p className="sixth-card-description">
                Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliq
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SixthSection;
