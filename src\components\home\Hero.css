.hero-section {
  padding: 5rem 0;
  background-color: #FFFBF7;
  overflow: hidden;
  position: relative;
}

/* Left side content */
.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #3A3053;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  max-width: 95%;
}

.hero-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 1rem;
  max-width: 95%;
}

.get-started-btn {
  background-color:  #FF7A45 !important; /* Bootstrap warning color to match the Sign-up button */
  border: none !important;
  padding: 0.75rem 2rem !important;
  font-weight: 600 !important;
  border-radius: 0.5rem !important;
  font-size: 1rem !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.2) !important;
  margin-top: 0.5rem;
  color: #fff !important; /* White text color to match the Sign-up button */
}

.get-started-btn:hover {
  background-color:  #e86a38 !important; /* Darker shade of warning color for hover */
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 193, 7, 0.3) !important;
}

/* Right side hero image */
.hero-widgets {
  position: relative;
  padding-left: 2rem;
}

.hero-image-container {
  position: relative;
  height: 450px;
  max-width: 600px;
  margin-left: auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hero-element-image {
  max-width: 100%;
  height: auto;
  max-height: 450px;
  object-fit: contain;
  position: relative;
  z-index: 2;
}

/* Responsive styles */
@media (max-width: 1199.98px) {
  .hero-image-container {
    height: 400px;
  }

  .hero-element-image {
    max-height: 400px;
  }
}

@media (max-width: 991.98px) {
  .hero-section {
    padding: 3rem 0;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-content {
    margin-bottom: 3rem;
  }

  .hero-image-container {
    height: 400px;
    max-width: 100%;
  }
}

@media (max-width: 767.98px) {
  .hero-title {
    font-size: 1.75rem;
  }

  .hero-image-container {
    height: 350px;
    display: flex;
    justify-content: center;
  }

  .hero-element-image {
    max-height: 350px;
  }
}

@media (max-width: 575.98px) {
  .hero-section {
    padding: 2rem 0;
  }

  .hero-title {
    font-size: 1.5rem;
  }

  .hero-image-container {
    height: 300px;
  }

  .hero-element-image {
    max-height: 300px;
  }
}
