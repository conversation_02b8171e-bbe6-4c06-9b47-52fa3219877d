.new-footer {
  background-color: #484848;
  color: #d1d5db; /* gray-300 */
  padding: 3rem 0 1rem;
  font-size: 0.95rem;
  font-family: Arial, sans-serif;
  margin-top: 4rem;
  clear: both;
  position: relative;
  z-index: 1;
}

/* Newsletter Section */
.newsletter-section {
  margin-bottom: 2rem;
}

.logo-container {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  position: relative;
}

.logo-container img {
  position: relative;
  z-index: 1;
}

/* Experimental: Try to make text white while keeping icon colors */
.logo-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, transparent 0%, transparent 30%, white 30%, white 100%);
  mix-blend-mode: multiply;
  pointer-events: none;
  z-index: 2;
}

.newsletter-text {
  color: #d1d5db;
  margin-bottom: 0;
  font-size: 0.95rem;
  line-height: 1.4;
}

.newsletter-form {
  display: flex;
  margin-bottom: 0.5rem;
}

.newsletter-form .form-control {
  border-radius: 50px;
  border: none;
  padding: 0.75rem 1.5rem;
  height: auto;
  box-shadow: none;
  flex-grow: 1;
}

.btn-subscribe {
  background-color: #FF7A45;
  color: white;
  border-radius: 50px;
  border: 1px solid #FF7A45;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  margin-left: 0.5rem;
  cursor: pointer;
}

.btn-subscribe:hover {
  background-color: #e86a38;
}

.consent-text {
  color: #b0b0b0;
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

/* Footer Divider */
.footer-divider {
  border-color: #555555;
  margin: 2rem 0;
  opacity: 0.3;
}

/* Footer Links Section */
.footer-links {
  margin-bottom: 2rem;
}

.footer-heading {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1.25rem;
  color: #ffffff;
}

.footer-list {
  list-style: none;
  padding-left: 0;
  margin-bottom: 2rem;
}

.footer-list li {
  margin-bottom: 0.75rem;
}

.footer-list a {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.2s;
  font-size: 0.95rem;
}

.footer-list a:hover {
  color: #FF7A45;
}

/* Contact Info */
.contact-info {
  margin-bottom: 1rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.contact-item i {
  margin-right: 0.75rem;
  margin-top: 0.25rem;
  color: #d1d5db;
}

.contact-item p, .contact-item a {
  color: #d1d5db;
  text-decoration: none;
  margin-bottom: 0;
  font-size: 0.95rem;
}

.contact-item a:hover {
  color: #FF7A45;
}

/* Social Icons */
.social-icons-top {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.social-icon-circle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #3a3a3a;
  color: #d1d5db;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 1rem;
}

.social-icon-circle:hover {
  background-color: #FF7A45;
  color: #ffffff;
}

/* App Download Buttons */
.app-download {
  display: flex;
  flex-direction: row;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.app-button {
  display: block;
  background-color: #333333;
  border-radius: 8px;
  border: 1px solid #ffffff;
  padding: 8px 12px;
  width: 160px;
  height: 48px;
  text-align: left;
  text-decoration: none;
}

.button-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.google-play-logo {
  width: 24px;
  height: 24px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="white" d="M325.3 234.3L104.6 13l280.8 161.2-60.1 60.1zM47 0C34 6.8 25.3 19.2 25.3 35.3v441.3c0 16.1 8.7 28.5 21.7 35.3l256.6-256L47 0zm425.6 225.6l-58.9-34.1-65.7 64.5 65.7 64.5 60.1-34.1c18-14.3 18-46.5-1.2-60.8zM104.6 499l280.8-161.2-60.1-60.1L104.6 499z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  margin-right: 12px;
}

.apple-logo {
  width: 20px;
  height: 24px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512"><path fill="white" d="M318.7 268.7c-.2-36.7 16.4-64.4 50-84.8-18.8-26.9-47.2-41.7-84.7-44.6-35.5-2.8-74.3 20.7-88.5 20.7-15 0-49.4-19.7-76.4-19.7C63.3 141.2 4 184.8 4 273.5q0 39.3 14.4 81.2c12.8 36.7 59 126.7 107.2 125.2 25.2-.6 43-17.9 75.8-17.9 31.8 0 48.3 17.9 76.4 17.9 48.6-.7 90.4-82.5 102.6-119.3-65.2-30.7-61.7-90-61.7-91.9zm-56.6-164.2c27.3-32.4 24.8-61.9 24-72.5-24.1 1.4-52 16.4-67.9 34.9-17.5 19.8-27.8 44.3-25.6 71.9 26.1 2 49.9-11.4 69.5-34.3z"/></svg>');
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  margin-right: 12px;
}

.button-text {
  display: flex;
  flex-direction: column;
}

.small-text {
  font-size: 8px;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.large-text {
  font-size: 16px;
  color: white;
  font-weight: 500;
}

/* InTech Platform Section */
.intech-platform {
  text-align: center;
  margin: 2rem 0;
}

.intech-platform p {
  font-size: 1rem;
  margin-bottom: 1.25rem;
  color: #d1d5db;
}

.platform-logos {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 3rem;
  flex-wrap: wrap;
  margin-top: 1.5rem;
}

.platform-logos .logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  width: 160px;
}

.platform-logos img {
  max-height: 60px;
  max-width: 160px;
  object-fit: contain;
  filter: brightness(1.2);
}

/* Copyright Section */
.copyright-section {
  text-align: center;
  margin: 2rem 0;
}

.copyright-section p {
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
  color: #d1d5db;
}

.social-icons-bottom {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin: 1.25rem 0;
}

.social-icons-bottom a {
  color: #d1d5db;
  font-size: 1.2rem;
  transition: color 0.2s;
}

.social-icons-bottom a:hover {
  color: #FF7A45;
}

/* Disclosure Section */
.disclosure-section {
  margin-top: 2rem;
  border-top: 1px solid #555555;
  padding-top: 2rem;
}

.disclosure-title {
  text-align: center;
  font-size: 0.95rem;
  margin-bottom: 1.5rem;
  letter-spacing: 1px;
  font-weight: normal;
  color: #d1d5db;
}

.disclosure-content {
  margin-bottom: 1.5rem;
}

.disclosure-content p {
  font-size: 0.85rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  color: #b0b0b0;
  text-align: justify;
}

.disclosure-footer {
  font-size: 0.85rem;
  color: #b0b0b0;
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
  .newsletter-form {
    flex-direction: column;
  }

  .btn-subscribe {
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .footer-heading {
    margin-top: 1.5rem;
  }

  .platform-logos {
    gap: 1rem;
  }

  .app-download {
    flex-direction: row;
    align-items: center;
  }
}
