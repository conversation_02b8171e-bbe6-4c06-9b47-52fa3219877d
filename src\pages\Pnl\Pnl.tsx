import React, { useEffect, useState } from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import axios from 'axios';
import { useParams } from "react-router-dom";

interface Position {
  security: string;
  position_type: string;
  buy_price: number;
  current_price: number;
  quantity: number;
  pnl: number;
}

const PnLSimulator: React.FC = () => {
  const [netPNL, setNetPNL] = useState<number | null>(null);
  const [longPNL, setLongPNL] = useState<number | null>(null);
  const [shortPNL, setShortPNL] = useState<number | null>(null);
  const [positions, setPositions] = useState<Position[]>([]);
  const [initialLoad, setInitialLoad] = useState<boolean>(true); // Only for first-time loading

 const { symbol } = useParams<{ symbol: string }>();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(
          `https://api.capovex.in/arbitrage/arbitrage_for_symbol_day?symbol=${symbol}`,
          {
            auth: {
              username: 'admin',
              password: 'admin123',
            },
            headers: {
              'Content-Type': 'application/json',
              Accept: 'application/json',
            },
          }
        );

        const result = response.data?.results?.[0];
        if (result) {
          setNetPNL(result.net_pnl);
          setLongPNL(result.long_pnl);
          setShortPNL(result.short_pnl);

          const lotSize = result?.lot_size || 0;

          const positionDetails: Position[] = [
            {
              security: symbol,
              position_type: 'SPOT LONG',
              buy_price: result?.long_buy_price || 0,
              current_price: result?.spot_price || 0,
              quantity: lotSize,
              pnl: result?.long_pnl || 0,
            },
            {
              security: result?.future_symbol || `${symbol}165MAY25`,
              position_type: 'FUTURE SHORT',
              buy_price: result?.short_sell_price || 0,
              current_price: result?.futures_price || 0,
              quantity: lotSize,
              pnl: result?.short_pnl || 0,
            },
          ];

          setPositions(positionDetails);
        }
      } catch (error: any) {
        console.error('❌ Error fetching data:', error?.response || error?.message || error);
      } finally {
        if (initialLoad) setInitialLoad(false);
      }
    };

    fetchData(); // Initial fetch

    const interval = setInterval(fetchData, 1000); 

    return () => clearInterval(interval); 
  }, []);

  return (
    <div className="container my-5">
      <h2 className="mb-2">Arbitrage PnL Simulator</h2>
      <p className="text-muted">Real Time PnL Simulator Adjusting to all charges & Brokerage</p>

      <div className="row my-4">
        {[
          { title: 'Net PNL', value: netPNL },
          { title: 'Long PNL', value: longPNL },
          { title: 'Short PNL', value: shortPNL },
        ].map((item, index) => (
          <div className="col-md-4 mb-3" key={index}>
            <div className="bg-dark text-white text-center py-4 rounded shadow-sm">
              <h6>{item.title}</h6>
              <h4>₹ {item.value?.toFixed(2) ?? '-'}</h4>
            </div>
          </div>
        ))}
      </div>

      <h5 className="mt-4 mb-3 fw-bold">Detailed Breakdown for {symbol}</h5>
      <div className="table-responsive">
        <table className="table table-bordered table-hover">
          <thead className="table-light">
            <tr>
              <th>Security</th>
              <th>Position Type</th>
              <th>Buying Price</th>
              <th>Quantity</th>
              <th>Current Price</th>
              <th>PNL</th>
            </tr>
          </thead>
          <tbody>
            {initialLoad ? (
              <tr>
                <td colSpan={6} className="text-center">Loading...</td>
              </tr>
            ) : (
              positions.map((pos, index) => (
                <tr key={index}>
                  <td>{pos.security}</td>
                  <td>{pos.position_type}</td>
                  <td>₹{pos.buy_price.toFixed(2)}</td>
                  <td>{pos.quantity}</td>
                  <td>₹{pos.current_price.toFixed(2)}</td>
                  <td className={pos.pnl >= 0 ? 'text-success' : 'text-danger'}>
                    ₹{pos.pnl.toFixed(2)}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PnLSimulator;
