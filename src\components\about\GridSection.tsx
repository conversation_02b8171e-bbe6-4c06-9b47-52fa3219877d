import React from 'react';

interface GridItem {
  title: string;
  description: string;
  icon: string;
}

const GridSection: React.FC = () => {
  const gridItems: GridItem[] = [
    {
      title: 'Responsive Design',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.',
      icon: 'bi-phone'
    },
    {
      title: 'Modern Framework',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.',
      icon: 'bi-code-square'
    },
    {
      title: 'Cloud Integration',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.',
      icon: 'bi-cloud-check'
    },
    {
      title: 'Analytics Dashboard',
      description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt.',
      icon: 'bi-graph-up'
    }
  ];

  return (
    <section className="grid-section">
      <div className="container">
        <div className="row">
          <div className="col-lg-8 mx-auto text-center">
            <h2 className="grid-title">Grid style for flexibility</h2>
            <p className="grid-subtitle">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod 
              tempor incididunt ut labore et dolore magna aliqua.
            </p>
          </div>
        </div>
        <div className="row mt-5">
          {gridItems.map((item, index) => (
            <div key={index} className="col-lg-6 col-md-6 mb-4">
              <div className="grid-card">
                <div className="grid-card-header">
                  <div className="grid-icon">
                    <i className={`bi ${item.icon}`}></i>
                  </div>
                  <h3 className="grid-card-title">{item.title}</h3>
                </div>
                <p className="grid-card-description">{item.description}</p>
                <div className="grid-card-footer">
                  <a href="#" className="grid-link">
                    Explore <i className="bi bi-arrow-right"></i>
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default GridSection;
