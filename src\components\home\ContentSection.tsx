import React from 'react';
import './ContentSection.css';
import { FaArrowRight, FaCheck } from 'react-icons/fa';
import contentPartImage from '../../assets/images/contentPart.png';

const ContentSection: React.FC = () => {
  return (
    <section className="content-section">
      <div className="container">
        <div className="content-section-wrapper">
          {/* Left side - Image with cards */}
          <div className="content-image-container">
            <img src={contentPartImage} alt="Content Part" className="content-part-image" />
          </div>

          {/* Right side - Text content */}
          <div className="content-info">
            <span className="exclusive-tag">Exclusive Opportunities</span>
            <h2 className="section-title">
              Structured Fin-Products<br />
              not to be found elsewhere
            </h2>
            <p className="section-description">
              Stellar is more than just a SaaS and technology template—it's a
              complete digital transformation solution.
            </p>

            <div className="feature-list">
              <div className="feature-item">
                <div style={{ marginRight: '0.75rem', marginTop: '0.25rem' }}>
                  <div style={{ width: '20px', height: '20px', borderRadius: '50%', backgroundColor: '#333', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <FaCheck style={{ color: 'white', fontSize: '0.7rem' }} />
                  </div>
                </div>
                <div style={{ color: '#555', fontSize: '0.95rem', lineHeight: 1.5 }}>
                  Access research and analytics in multiple regional languages
                </div>
              </div>

              <div className="feature-item">
                <div style={{ marginRight: '0.75rem', marginTop: '0.25rem' }}>
                  <div style={{ width: '20px', height: '20px', borderRadius: '50%', backgroundColor: '#333', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <FaCheck style={{ color: 'white', fontSize: '0.7rem' }} />
                  </div>
                </div>
                <div style={{ color: '#555', fontSize: '0.95rem', lineHeight: 1.5 }}>
                  Gain insights from markets, industries, and diverse asset classes.
                </div>
              </div>

              <div className="feature-item">
                <div style={{ marginRight: '0.75rem', marginTop: '0.25rem' }}>
                  <div style={{ width: '20px', height: '20px', borderRadius: '50%', backgroundColor: '#333', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <FaCheck style={{ color: 'white', fontSize: '0.7rem' }} />
                  </div>
                </div>
                <div style={{ color: '#555', fontSize: '0.95rem', lineHeight: 1.5 }}>
                  Utilize tools designed for both amateur and professional investors
                </div>
              </div>

              <div className="feature-item">
                <div style={{ marginRight: '0.75rem', marginTop: '0.25rem' }}>
                  <div style={{ width: '20px', height: '20px', borderRadius: '50%', backgroundColor: '#333', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <FaCheck style={{ color: 'white', fontSize: '0.7rem' }} />
                  </div>
                </div>
                <div style={{ color: '#555', fontSize: '0.95rem', lineHeight: 1.5 }}>
                  Streamlines complex studies into a connected ecosystem
                </div>
              </div>
            </div>

            <div className="trial-button-container">
              <a href="/trial" className="trial-button">
                Get Free Trial <span className="arrow-icon"><FaArrowRight /></span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContentSection;
