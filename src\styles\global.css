/* body {
  background-color: #FFFBF7;
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
}

html {
  background-color: #FFFBF7;
}

/* NUCLEAR CSS RESET - Prevent all positioning conflicts */
/* main {
  display: block !important;
  position: static !important;
  z-index: auto !important;
  transform: none !important;
  float: none !important;
}

section {
  display: block !important;
  position: static !important;
  z-index: auto !important;
  transform: none !important;
  float: none !important;
  clear: both;
  width: 100%; */
  /* Remove margin: 0 !important to allow proper spacing */
/* } */

/* Reset any Bootstrap or global overrides */
/* .hero-section,
.features-section,
.language-carousel-section { */
  /* position: static !important;
  z-index: auto !important;
  transform: none !important;
  float: none !important;
  top: auto !important;
  left: auto !important;
  right: auto !important;
  bottom: auto !important;
  clear: both;
} */

/* Add proper spacing between sections */
/* .features-section {
  margin-bottom: 3rem !important;
}

.language-carousel-section {
  margin-top: 3rem !important;
} */ 

body {
  background-color: #FFFBF7;
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
}

html {
  background-color: #FFFBF7;
}
