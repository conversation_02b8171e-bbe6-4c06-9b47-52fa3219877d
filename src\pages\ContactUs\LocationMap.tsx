import React from "react";
import "./LocationMap.css";
import LocationMapImage from "../../assets/images/LocationMap.png";

const LocationMap = () => {
  return (
    <div className="container location-map-container py-5 mt-5">
      <div className="row">
        {/* Text Column */}
        <div className="col-md-6 mb-4">
          <div className="location-map-text">
            <div className="mb-4">
              <h4 className="h1-bold">HQ</h4>
              <p>
                Capovex, 5th Floor, J35/D-T,<br />
                Central Avenues Road, J Block, Brahma City,<br />
                Golf Course Road Extention, Sector 63,<br />
                Gurugram, Haryana - 122101
              </p>
            </div>

            <div className="mb-4">
              <h5 className="h2-bold">Compliance Center</h5>
              <p>
                NIRMAN HOUSE, Plot No 8,Zone - 1,<br />
                Maharana Prata<PERSON> Nagar,<br />
                Bhopal, Madhya Pradesh - 462011
              </p>
            </div>

            <div className="mb-4">
              <h5 className="h2-bold">Technology Center</h5>
              <p>
                Capovex, Unit No 4, First Floor<br />
                Coral Arihant Tower, Rampura Road,<br />
                Near Patrakar Colony, Mansarovar, Jaipur
              </p>
            </div>

            <div className="mb-4">
              <h5 className="h2-bold">24+ Authorized Partners & Regional Offices</h5>
              <p>
                From Main Land to Lakshdweeps, we are<br />
                rapidly increasing our base of authorised<br />
                representatives to guide through<br />
                investment journey
              </p>
            </div>
          </div>
        </div>

        {/* Image Column */}
        <div className="col-md-6">
          <div className="location-map-image">
            <img
              src={LocationMapImage}
              alt="Location Map"
              className="img-fluid rounded"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default LocationMap;
