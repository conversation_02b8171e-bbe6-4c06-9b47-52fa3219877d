import React from 'react';
import './CtaSection.css';
import ctaImage from '../../assets/images/CTA.png';
import googlePlayIcon from '../../assets/images/GoogleButton.png';

const CtaSection: React.FC = () => {
  return (
    <section className="cta-section">
      <div className="container">
        <div className="cta-content">
          {/* Left side - Text and buttons */}
          <div className="cta-text">
            <h2 className="cta-title">Download The App</h2>
            <p className="cta-description">
              Wisdom new and valley answer. Contented it so is discourse recommend.
              Man its upon him call mile. An pasture he himself believe ferrars besides
              cottage.
            </p>
            <div className="app-buttons">
              <a href="https://www.apple.com/app-store/" className="app-button app-store">
                <div className="app-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M17.0415 12.5508C17.0195 9.61084 19.5015 8.17084 19.5995 8.11284C18.1975 6.12484 16.0555 5.88484 15.2755 5.86884C13.4635 5.67284 11.7115 6.95684 10.7915 6.95684C9.85154 6.95684 8.43954 5.88884 6.89954 5.91684C4.91954 5.94484 3.09954 7.05684 2.09954 8.77684C0.0395448 12.2728 1.60754 17.4628 3.57554 20.3468C4.55954 21.7548 5.71954 23.3308 7.26754 23.2748C8.77554 23.2148 9.35954 22.3308 11.1795 22.3308C12.9835 22.3308 13.5275 23.2748 15.1035 23.2388C16.7275 23.2148 17.7355 21.8228 18.6795 20.4028C19.8115 18.7628 20.2675 17.1468 20.2875 17.0668C20.2475 17.0548 17.0675 15.8268 17.0415 12.5508Z" fill="white"/>
                    <path d="M14.3275 3.77081C15.1275 2.79881 15.6675 1.49081 15.5115 0.166809C14.3875 0.214809 12.9955 0.950809 12.1595 1.89881C11.4195 2.73681 10.7675 4.09081 10.9435 5.36281C12.2115 5.46481 13.4915 4.72881 14.3275 3.77081Z" fill="white"/>
                  </svg>
                </div>
                <div className="app-text">
                  <span className="app-small">Download on the</span>
                  <span className="app-large">App Store</span>
                </div>
              </a>
              <a href="https://play.google.com/" className="app-button google-play">
                <div className="app-icon google-icon">
                  <img src={googlePlayIcon} alt="Google Play" className="google-play-icon" />
                </div>
                <div className="app-text">
                  <span className="app-small">GET IT ON</span>
                  <span className="app-large">Google Play</span>
                </div>
              </a>
            </div>
          </div>

          {/* Right side - App screenshot */}
          <div className="cta-image">
            <img src={ctaImage} alt="App screenshot" className="app-screenshot" />
          </div>
        </div>
      </div>
    </section>
  );
};

export default CtaSection;
