import React, { useState, useEffect } from 'react';
import './TestimonialSection.css';
import { Fa<PERSON>ser, FaChevronLeft, FaChevronRight } from 'react-icons/fa';

// Testimonial data
const testimonials = [
  {
    id: 1,
    quote: "Loved the tool!",
    text: "Being a regular investor & have used tons of tools out there in market. I am really excited to see such a powerfull tool and be a part of a first few beta users where we could give more insights as well.",
    name: "<PERSON><PERSON><PERSON>",
  },
  {
    id: 2,
    quote: "Transformed my Investment Insights",
    text: "So much Insights, super fast and at the same time connected to market activities. Never thought India could build something like this, on its own. Kudos to the Team and Developers.",
    name: "<PERSON><PERSON><PERSON>",
  },
  {
    id: 3,
    quote: "Game changer for my portfolio",
    text: "I've been using this platform for 3 months and have seen significant improvements in my investment decisions. The analytics are powerful yet easy to understand.",
    name: "<PERSON><PERSON>",
  },
  {
    id: 4,
    quote: "Exactly what I needed",
    text: "As a beginner investor, I was looking for something that could guide me without overwhelming me with jargon. This platform strikes the perfect balance.",
    name: "<PERSON><PERSON>",
  },
  {
    id: 5,
    quote: "Exceptional analytics tools",
    text: "The depth of analysis available is impressive. I can make informed decisions quickly with the comprehensive data visualization and real-time market insights.",
    name: "<PERSON>esh <PERSON>",
  },
  {
    id: 6,
    quote: "Simplified my investment strategy",
    text: "This platform has helped me develop a more structured approach to investing. The personalized recommendations are spot on and have improved my returns.",
    name: "Ananya Desai",
  },
];

const TestimonialSection: React.FC = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isPaused, setIsPaused] = useState(false);

  // Handle navigation
  const goToPrevious = () => {
    if (isAnimating) return;

    setIsAnimating(true);
    setIsPaused(true);
    setActiveIndex((prevIndex) =>
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );

    setTimeout(() => {
      setIsAnimating(false);
    }, 500);

    // Resume auto rotation after 5 seconds
    setTimeout(() => {
      setIsPaused(false);
    }, 5000);
  };

  const goToNext = () => {
    if (isAnimating) return;

    setIsAnimating(true);
    setIsPaused(true);
    setActiveIndex((prevIndex) =>
      (prevIndex + 1) % testimonials.length
    );

    setTimeout(() => {
      setIsAnimating(false);
    }, 500);

    // Resume auto rotation after 5 seconds
    setTimeout(() => {
      setIsPaused(false);
    }, 5000);
  };

  // Auto rotation
  useEffect(() => {
    const interval = setInterval(() => {
      if (!isPaused) {
        // Instead of calling goToNext which would cause a circular dependency,
        // we'll directly update the activeIndex
        setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [isPaused]);

  return (
    <section className="testimonial-section">
      <div className="container">
        <div className="testimonial-header">
          <h2 className="testimonial-title">What our Users have to say</h2>
          <p className="testimonial-subtitle">
            Users say it all. Whether you are a seasoned investor or a novice, Capovex is built for all.
          </p>
        </div>

        <div className="testimonial-carousel">
          <button
            className="carousel-arrow carousel-arrow-left"
            onClick={goToPrevious}
            aria-label="Previous testimonial"
          >
            <FaChevronLeft />
          </button>

          <div className={`testimonial-cards ${isAnimating ? 'animating' : ''}`}>
            <div className="testimonial-card">
              <div className="testimonial-icon">
                <FaUser />
              </div>
              <h3 className="testimonial-quote">{testimonials[activeIndex % testimonials.length].quote}</h3>
              <p className="testimonial-text">{testimonials[activeIndex % testimonials.length].text}</p>
              <p className="testimonial-name">{testimonials[activeIndex % testimonials.length].name}</p>
            </div>

            <div className="testimonial-card">
              <div className="testimonial-icon">
                <FaUser />
              </div>
              <h3 className="testimonial-quote">{testimonials[(activeIndex + 1) % testimonials.length].quote}</h3>
              <p className="testimonial-text">{testimonials[(activeIndex + 1) % testimonials.length].text}</p>
              <p className="testimonial-name">{testimonials[(activeIndex + 1) % testimonials.length].name}</p>
            </div>
          </div>

          <button
            className="carousel-arrow carousel-arrow-right"
            onClick={goToNext}
            aria-label="Next testimonial"
          >
            <FaChevronRight />
          </button>
        </div>
      </div>
    </section>
  );
};

export default TestimonialSection;
