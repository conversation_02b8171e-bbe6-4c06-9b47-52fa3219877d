.solution-section {
  padding: 5rem 0;
  background-color: #FFFBF7;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.solution-content {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  gap: 3rem;
  min-height: 600px;
}

.mobile-showcase {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-end;
}

.mobile-image {
  max-width: 100%;
  height: auto;
  max-height: 600px;
  object-fit: contain;
}

.solution-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding-bottom: 20px;
}

.solution-header {
  margin-bottom: 0;
}

.features-tag {
  display: inline-block;
  background-color: rgba(255, 107, 0, 0.1);
  color: #FF6B00;
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.section-title {
  font-size: 2.3rem;
  font-weight: 650;
  color: #333;
  line-height: 1.2;
  margin: 0;
  max-width: 90%;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem 2rem;
  margin-top: 3rem;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.feature-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 1rem;
}

.feature-icon {
  font-size: 1.5rem;
  color: #FF6B00;
}

.feature-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.feature-description {
  font-size: 0.9rem;
  line-height: 1.5;
  color: #666;
  margin: 0;
}

/* Responsive styles */
@media (max-width: 992px) {
  .solution-content {
    flex-direction: column;
    min-height: auto;
  }

  .solution-text {
    order: 2;
    min-height: auto;
    padding: 2rem 0;
  }

  .mobile-showcase {
    order: 1;
    margin-bottom: 2rem;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .section-title {
    font-size: 2rem;
    text-align: center;
  }

  .features-tag {
    display: block;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
    width: fit-content;
  }

  .solution-header {
    text-align: center;
  }
}

@media (max-width: 768px) {
  .features-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}
