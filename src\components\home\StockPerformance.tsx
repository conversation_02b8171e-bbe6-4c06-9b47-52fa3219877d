import React from 'react';
import './StockPerformance.css';
import { FaArrowRight } from 'react-icons/fa';
import stockContentImage from '../../assets/images/stockContent.png';

const StockPerformance: React.FC = () => {
  return (
    <section className="stock-performance-section">
      <div className="container">
        <div className="stock-performance-content">
          {/* Left side - Text content */}
          <div className="performance-info">
            <span className="dynamic-tag">A Dynamic Brokerage System</span>
            <h2 className="section-title">
              Trading at Arth: Comes<br />
              with ZERO Charges. ₹0
            </h2>
            <p className="section-description">
              Not just Zero Brokerage, Zero STT, Zero Transaction charges,<br />
              Zero GST. It's all Zero as long as you have that perfect score.
            </p>

            <ul className="feature-list">
              <li className="feature-item">
                <div className="feature-check">✓</div>
                <p className="feature-text">
                  Pay only if you are able to beat the benchmark<br />
                  but one that aligns with your risk profile.
                </p>
              </li>
              <li className="feature-item">
                <div className="feature-check">✓</div>
                <p className="feature-text">
                  Arth Rekha is your dynamic financial behaviour<br />
                  score that rewards you for right investing.
                </p>
              </li>
              <li className="feature-item">
                <div className="feature-check">✓</div>
                <p className="feature-text">
                  Bad at Investing behaviour, No Worries. We are<br />
                  sure you will learn the Arth Way
                </p>
              </li>
              <li className="feature-item">
                <div className="feature-check">✓</div>
                <p className="feature-text">
                  Fully research and tracked portfolios so you<br />
                  never lose any insights
                </p>
              </li>
            </ul>

            <div className="trial-button-container">
              <button className="trial-button">
                Open an Account
                <FaArrowRight className="button-arrow" />
              </button>
            </div>
          </div>
          
          {/* Right side - Stock content image */}
          <div className="stock-image-container">
            <img
              src={stockContentImage}
              alt="Stock Performance Dashboard"
              className="stock-content-image"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default StockPerformance;
