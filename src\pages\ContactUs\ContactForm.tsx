import React from 'react';
import { Container, Row, Col, <PERSON>, Button } from 'react-bootstrap';
import { MdPhone, MdEmail, MdLocationOn } from 'react-icons/md';
import  './ContactForm.css';

const ContactForm = () => {
  return (
    <div className="py-5 " style={{ backgroundColor: '#fffaf5' }}>
      <Container>
          <div className="text-center mb-5 pb-4 text-[#484848]">
            <h2 className="mb-3 fw-semibold">We’d Love To Hear From You</h2>
            <p className="mb-1">
              Whether you’re curious about features, a free trial, or even
            </p>
            <p>
               press. We’re ready to answer any and all questions.
            </p>
          </div>

       
        <Row>
           {/* Contact Info */}
          <Col md={6} className="mb-4">
         <h3 className="fw-semibold">Contact Information</h3>
         <p className="text-muted mb-5">Fill up the form and we’ll get back to you in few hours.</p>

         <div className="d-flex flex-column contact-info">
           <div className="d-flex align-items-start">
            <MdPhone style={{ color: '#ed7d22', fontSize: '1.3rem' }} className="me-3 fs-5" />
            <span className="text-muted">+91 70390 54321</span>
          </div>

          <div className="d-flex align-items-start">
            <MdEmail style={{ color: '#ed7d22', fontSize: '1.3rem' }} className="me-3 fs-5" />
            <span className="text-muted"><EMAIL></span>
          </div>

          <div className="d-flex align-items-start">
            <MdLocationOn style={{ color: '#ed7d22', fontSize: '1.3rem' }} className="me-3 fs-5" />
            <span className="text-muted">
              J-35/D-T, Sector 63, Gurugram, Haryana,<br /> Bharat 122101
            </span>
           </div>
         </div>



        <div className="d-flex gap-3 mt-5">
          {['facebook', 'twitter', 'instagram'].map((platform) => (
            <a
              key={platform}
              href="#"
              style={{
                width: '40px',
                height: '40px',
                border: '2px solid orange',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'orange',
                textDecoration: 'none',
                transition: 'all 0.3s ease',
              }}
              onMouseEnter={(e) => {
                (e.currentTarget as HTMLAnchorElement).style.backgroundColor = 'orange';
                (e.currentTarget as HTMLAnchorElement).style.color = 'white';
              }}
              onMouseLeave={(e) => {
                (e.currentTarget as HTMLAnchorElement).style.backgroundColor = 'transparent';
                (e.currentTarget as HTMLAnchorElement).style.color = 'orange';
              }}
            >
              <i className={`bi bi-${platform}`}></i>
            </a>
          ))}
        </div>


          </Col>



          {/* Form */}
          <Col md={6}>
            <Form>
              <Row className="mb-3">
                <Col>
                  <Form.Label>First Name</Form.Label>
                  <Form.Control placeholder="Placeholder" />
                </Col>
                <Col>
                  <Form.Label>Last Name</Form.Label>
                  <Form.Control placeholder="Placeholder" />
                </Col>
              </Row>

              <Form.Group className="mb-3">
                <Form.Label>Email ID</Form.Label>
                <Form.Control type="email" placeholder="Placeholder" />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Subject</Form.Label>
                <Form.Select>
                  <option>Placeholder</option>
                  <option>General Inquiry</option>
                  <option>Support</option>
                  <option>Feedback</option>
                </Form.Select>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Message</Form.Label>
                <Form.Control as="textarea" placeholder="Placeholder" />
              </Form.Group>

              <div className="d-flex justify-content-center">
                <Button 
                  style={{
                    backgroundColor: '#ed7d22',
                    borderColor: '#ed7d22',
                    fontWeight: 'bold',
                    color: 'white',
                    padding: '0.5rem 1.5rem',
                    fontSize: '1rem',
                    
                  }}
                >
                  Submit
                </Button>
              </div>


              <p className="mt-3 text-muted small text-center">
                We will respond to you within next 48 working hours
              </p>
            </Form>
          </Col>
        </Row>
      </Container>
    </div>
  );
};

export default ContactForm;
