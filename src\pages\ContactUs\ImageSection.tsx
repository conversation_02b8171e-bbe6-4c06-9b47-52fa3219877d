import React from "react";
import "./ImageSection.css";

import NewImage from "../../assets/images/NewImage.png";
import MapImage from "../../assets/images/map.png";

const ImageSection: React.FC = () => {
  return (
    <div className="image-section">
      {/* Background image */}
      <img src={NewImage} alt="Building" className="bg-image" />

      {/* Content */}
      <div className="content-container">
        <div className="text-content">
          <h2 className="fw-bold">
            We are based in the <br /> Millennium City of Bharat
          </h2>
          <p>
            At Capovex Research and Analytics, we treasure clarity and precision just as we
            cherish our roots in the vibrant and historic Gurugram—once known as the Town of Guru, a
            place steeped in the sagas of the Mahabharata, where teachers (gurus) imparted wisdom
            that transcended the ages.
          </p>
        </div>

        <div className="map-wrapper">
          <img src={MapImage} alt="Map" className="map-overlay" />
        </div>
      </div>
    </div>
  );
};

export default ImageSection;
