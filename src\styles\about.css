/* About Page Styles */
.about-page {
  min-height: 100vh;
  padding-top: 80px; /* Account for fixed navbar */
  background-color: #FFFBF7;
}

/* First Section - Hero */
.about-hero-section {
  display: flex;
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 100px;
  flex-direction: column;
  align-items: center;
  gap: 120px;
  background-color: #FFFBF7;
}

.about-hero-content {
  text-align: center;
  max-width: 800px;
}

.about-hero-subtitle {
  font-size: 1rem;
  color: #486284; /* First line color */
  margin-bottom: 1rem;
  font-weight: 400;
}

.about-hero-title {
  font-size: 3rem;
  font-weight: 700;
  color: #486284; /* Second line color */
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.about-hero-description {
  font-size: 1.1rem;
  color: #8CA2C0; /* Third paragraph color */
  margin-bottom: 0;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Second Section - Wireframe Kit */
.about-wireframe-section {
  display: flex;
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 100px 120px;
  justify-content: space-between;
  align-items: center;
  background-color: #FFFBF7;
}

.about-wireframe-container {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}

.about-wireframe-left {
  flex: 1;
  max-width: 400px;
}

.about-wireframe-title {
  font-size: 3rem;
  font-weight: 700;
  color: #484848;
  margin-bottom: 2rem;
  line-height: 1.2;
}

.about-wireframe-description {
  font-size: 1.1rem;
  color: #484848;
  line-height: 1.6;
  margin-bottom: 2.5rem;
}

.about-wireframe-button {
  background-color: #484848;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.about-wireframe-button:hover {
  background-color: #3a3a3a;
}

.about-wireframe-right {
  flex: 1;
  max-width: 600px;
}

.about-wireframe-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.about-wireframe-card {
  padding: 30px 24px;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.about-wireframe-card:hover {
  transform: translateY(-2px);
}

/* First card is dark by default */
.about-wireframe-card.dark {
  background-color: #484848;
  color: white;
}

/* Other cards are white by default */
.about-wireframe-card.light {
  background-color: #ffffff;
  color: #333;
  border: 1px solid #E9ECEF;
}

.about-card-icon {
  width: 48px;
  height: 48px;
  background-color: #FF8C42;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  color: white;
}

.about-wireframe-card.dark .about-card-icon {
  background-color: #FF8C42;
}

.about-wireframe-card.light .about-card-icon {
  background-color: #FF8C42;
}

.about-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 12px;
  line-height: 1.3;
}

.about-wireframe-card.dark .about-card-title {
  color: white;
}

.about-wireframe-card.light .about-card-title {
  color: #333;
}

.about-card-description {
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0;
}

.about-wireframe-card.dark .about-card-description {
  color: #D1D5DB;
}

.about-wireframe-card.light .about-card-description {
  color: #6B7280;
}

/* Third Section - Designed to Think */
.about-third-section-wrapper {
  width: 100%;
  background: #1a1a1a;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 622"><defs><pattern id="circuit" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse"><path d="M10,10 L90,10 M10,50 L90,50 M10,90 L90,90 M10,10 L10,90 M50,10 L50,90 M90,10 L90,90" stroke="%23333" stroke-width="1" fill="none"/></pattern></defs><rect width="100%" height="100%" fill="url(%23circuit)"/></svg>');
}

.about-third-section {
  display: flex;
  width: 100%;
  max-width: 1440px;
  height: 622px;
  margin: 0 auto;
  padding: 56px 120px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  position: relative;
}

.about-third-section-container {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 2;
}

.about-third-section-left {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.about-third-image {
  max-width: 400px;
  opacity: 0.8;
}

.about-third-image img {
  width: 100%;
  height: auto;
  filter: brightness(0.8) contrast(1.2);
}

.about-third-content {
  flex: 1;
  max-width: 500px;
  text-align: left;
  color: white;
}

.about-third-title {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.about-third-description {
  font-size: 1.1rem;
  color: #e0e0e0;
  line-height: 1.6;
  margin: 0;
}

/* About Features Section */
.about-features-section {
  display: flex;
  width: 100%;
  max-width: 1440px;
  height: 733px;
  margin: 0 auto;
  padding: 100px 120px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 100px;
  background: #FFFBF7;
}

.about-features-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 100px;
  width: 100%;
}

.about-features-title {
  font-size: 3rem;
  font-weight: 700;
  color: #484848;
  text-align: center;
  margin: 0;
  line-height: 1.2;
  max-width: 800px;
}

.about-features-grid {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  gap: 60px;
  width: 100%;
}

.about-feature-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  flex: 1;
  max-width: 300px;
}

.about-feature-icon {
  width: 48px;
  height: 48px;
  background-color: #484848;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.about-feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #484848;
  margin: 0;
  line-height: 1.3;
}

.about-feature-description {
  font-size: 1rem;
  color: #6B7280;
  line-height: 1.6;
  margin: 0;
}

/* Fifth Section - Intelligence at Every Touchpoint */
.fifth-section-wrapper {
  width: 100%;
  background: #1a1a1a;
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 622"><defs><pattern id="circuit2" x="0" y="0" width="80" height="80" patternUnits="userSpaceOnUse"><path d="M5,5 L75,5 M5,40 L75,40 M5,75 L75,75 M5,5 L5,75 M40,5 L40,75 M75,5 L75,75" stroke="%23333" stroke-width="0.8" fill="none"/><circle cx="20" cy="20" r="2" fill="%23444"/><circle cx="60" cy="60" r="2" fill="%23444"/></pattern></defs><rect width="100%" height="100%" fill="url(%23circuit2)"/></svg>');
}

.fifth-section {
  display: flex;
  width: 100%;
  max-width: 1440px;
  height: 622px;
  margin: 0 auto;
  padding: 56px 120px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
  position: relative;
}

.fifth-section-container {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: space-between;
}

.fifth-section-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 24px;
  max-width: 500px;
}

.fifth-section-right {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fifth-image {
  max-width: 400px;
  opacity: 0.9;
}

.fifth-image img {
  width: 100%;
  height: auto;
  filter: brightness(0.9) contrast(1.1);
}

.fifth-title {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  margin: 0;
  line-height: 1.2;
}

.fifth-description {
  font-size: 1.1rem;
  color: #e0e0e0;
  line-height: 1.6;
  margin: 0;
}

/* Sixth Section - Grid Style */
.sixth-section {
  display: flex;
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 100px 150px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 48px;
  background: #FFFBF7;
}

.sixth-section-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 48px;
  width: 100%;
}

.sixth-section-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.sixth-title {
  font-size: 3rem;
  font-weight: 700;
  color: #484848;
  margin: 0;
  line-height: 1.2;
}

.sixth-subtitle {
  font-size: 1.1rem;
  color: #6B7280;
  line-height: 1.6;
  margin: 0;
  text-align: center;
}

.sixth-cards-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  width: 100%;
  max-width: 1000px;
}

.sixth-card {
  display: flex;
  padding: 40px 32px;
  flex-direction: row;
  align-items: flex-start;
  gap: 24px;
  background: #E8EBF0;
  border-radius: 16px;
  transition: all 0.3s ease;
  min-height: 160px;
}

.sixth-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.sixth-card-image {
  width: 100px;
  height: 100px;
  background: #F8F9FA;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #E5E7EB;
  flex-shrink: 0;
}

.sixth-card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  flex: 1;
}

.sixth-card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
  line-height: 1.5;
}

.sixth-card-description {
  font-size: 0.875rem;
  color: #6B7280;
  line-height: 1.6;
  margin: 0;
}

/* Seventh Section - Statistics - Full Width Background */
.seventh-section-wrapper {
  width: 100%;
  background: #484848;
}

.seventh-section {
  display: flex;
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 100px 164px;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.seventh-section-container {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  gap: 80px;
}

.seventh-stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px 80px;
  flex: 1;
  max-width: 500px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.stat-number {
  font-size: 4rem;
  font-weight: 700;
  color: #FFFFFF;
  line-height: 1;
  margin: 0;
}

.stat-label {
  font-size: 0.875rem;
  color: #D1D5DB;
  line-height: 1.4;
  margin: 0;
}

.seventh-content {
  flex: 1;
  max-width: 500px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.seventh-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0;
  line-height: 1.2;
}

.seventh-description {
  font-size: 1rem;
  color: #D1D5DB;
  line-height: 1.6;
  margin: 0;
}

/* Eighth Section - Trusted Brands */
.eighth-section {
  display: flex;
  width: 100%;
  max-width: 1440px;
  height: 428px;
  margin: 0 auto;
  padding: 100px 120px;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  background: #FFFBF7;
}

.eighth-section-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;
  gap: 60px;
}

.eighth-section-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.eighth-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #484848;
  margin: 0;
  line-height: 1.2;
}

.eighth-subtitle {
  font-size: 1.1rem;
  color: #6B7280;
  line-height: 1.6;
  margin: 0;
  text-align: center;
}

.brands-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 80px;
  width: 100%;
  flex-wrap: wrap;
}

.brand-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  opacity: 0.7;
  transition: all 0.3s ease;
  filter: grayscale(100%);
}

.brand-logo:hover {
  opacity: 1;
  filter: grayscale(0%);
  transform: translateY(-2px);
}

.brand-logo img {
  height: 100%;
  width: auto;
  object-fit: contain;
  max-width: 150px;
}

/* Responsive Design */
@media (max-width: 1440px) {
  .about-hero {
    width: 100%;
    padding: 80px 40px;
  }

  .wireframe-section {
    width: 100%;
    padding: 80px 60px;
  }

  .third-section-wrapper {
    width: 100%;
  }

  .third-section {
    width: 100%;
    padding: 56px 60px;
    height: auto;
    min-height: 500px;
  }

  .features-section {
    width: 100%;
    padding: 80px 60px;
    height: auto;
    min-height: 600px;
    gap: 80px;
  }

  .fifth-section-wrapper {
    width: 100%;
  }

  .fifth-section {
    width: 100%;
    padding: 56px 60px;
    height: auto;
    min-height: 500px;
  }

  .sixth-section {
    width: 100%;
    padding: 80px 60px;
  }

  .seventh-section-wrapper {
    width: 100%;
  }

  .seventh-section {
    width: 100%;
    padding: 80px 60px;
  }

  .seventh-section-container {
    gap: 60px;
  }

  .seventh-stats-grid {
    gap: 40px 60px;
  }

  .eighth-section {
    width: 100%;
    padding: 80px 60px;
    height: auto;
    min-height: 350px;
  }

  .brands-container {
    gap: 60px;
  }
}

@media (max-width: 768px) {
  .about-page {
    padding-top: 70px;
  }

  .about-hero-title,
  .about-wireframe-title,
  .about-third-title,
  .about-features-title,
  .fifth-title,
  .sixth-title,
  .seventh-title,
  .eighth-title {
    font-size: 2.2rem;
  }

  .about-hero-section,
  .about-wireframe-section {
    padding: 60px 20px;
    gap: 60px;
  }

  .about-third-section-wrapper {
    width: 100%;
  }

  .about-third-section {
    padding: 40px 20px;
    height: auto;
    min-height: 400px;
    align-items: center;
  }

  .about-third-section-container {
    flex-direction: column;
    gap: 30px;
  }

  .about-third-section-left,
  .about-third-content {
    flex: none;
    width: 100%;
    max-width: 100%;
  }

  .about-third-content {
    text-align: center;
  }

  .about-third-image {
    max-width: 300px;
  }

  .about-features-section {
    padding: 60px 20px;
    height: auto;
    min-height: 500px;
    gap: 60px;
  }

  .fifth-section-wrapper {
    width: 100%;
  }

  .fifth-section {
    padding: 40px 20px;
    height: auto;
    min-height: 400px;
    align-items: center;
  }

  .about-hero-content {
    max-width: 100%;
  }

  .about-wireframe-container {
    flex-direction: column;
    gap: 40px;
    justify-content: center;
    align-items: center;
  }

  .about-wireframe-left,
  .about-wireframe-right {
    max-width: 100%;
  }

  .about-wireframe-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .about-wireframe-card {
    padding: 24px 20px;
  }

  .about-third-section-container {
    justify-content: center;
  }

  .about-third-content {
    text-align: center;
    max-width: 100%;
  }

  .about-features-grid {
    flex-direction: column;
    gap: 40px;
    align-items: center;
  }

  .about-feature-card {
    max-width: 100%;
    text-align: center;
    align-items: center;
  }

  .about-features-container {
    gap: 60px;
  }

  .fifth-section-container {
    flex-direction: column;
    gap: 30px;
  }

  .fifth-section-left,
  .fifth-section-right {
    flex: none;
    width: 100%;
  }

  .fifth-section-left {
    text-align: center;
    align-items: center;
    max-width: 100%;
  }

  .fifth-image-placeholder {
    width: 300px;
    height: 200px;
  }

  .sixth-section {
    padding: 60px 20px;
    gap: 40px;
  }

  .sixth-section-container {
    gap: 40px;
  }

  .sixth-cards-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    max-width: 100%;
  }

  .sixth-card {
    padding: 24px 20px;
    flex-direction: column;
    gap: 16px;
    min-height: auto;
  }

  .sixth-card-image {
    width: 80px;
    height: 80px;
    align-self: flex-start;
  }

  .seventh-section-wrapper {
    width: 100%;
  }

  .seventh-section {
    padding: 60px 20px;
  }

  .seventh-section-container {
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }

  .seventh-stats-grid {
    gap: 30px 40px;
    max-width: 100%;
    justify-items: center;
  }

  .stat-item {
    align-items: center;
    text-align: center;
  }

  .stat-number {
    font-size: 3rem;
  }

  .seventh-content {
    max-width: 100%;
    text-align: center;
  }

  .eighth-section {
    padding: 60px 20px;
    height: auto;
    min-height: 300px;
  }

  .eighth-section-container {
    gap: 40px;
  }

  .brands-container {
    gap: 40px;
    justify-content: center;
  }

  .brand-logo {
    height: 50px;
  }

  .brand-logo img {
    max-width: 120px;
  }
}
