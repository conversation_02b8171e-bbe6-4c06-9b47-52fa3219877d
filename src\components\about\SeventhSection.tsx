import React, { useState, useEffect, useRef } from 'react';

interface CounterProps {
  end: number;
  suffix?: string;
  duration?: number;
}

const Counter: React.FC<CounterProps> = ({ end, suffix = '', duration = 2000 }) => {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const counterRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
        }
      },
      { threshold: 0.5 }
    );

    if (counterRef.current) {
      observer.observe(counterRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  useEffect(() => {
    if (!isVisible) return;

    let startTime: number;
    const startValue = 1;

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);

      const currentCount = Math.floor(startValue + (end - startValue) * progress);
      setCount(currentCount);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [isVisible, end, duration]);

  return (
    <div ref={counterRef} className="stat-number">
      {count}{suffix}
    </div>
  );
};

const SeventhSection: React.FC = () => {
  return (
    <div className="seventh-section-wrapper">
      <section className="seventh-section">
        <div className="seventh-section-container">
          <div className="seventh-stats-grid">
            <div className="stat-item">
              <Counter end={12}  suffix="+"/>
              <div className="stat-label">
                Projects<br />
                stats here
              </div>
            </div>

            <div className="stat-item">
              <Counter end={100} suffix="k+" />
              <div className="stat-label">
                Projects<br />
                stats here
              </div>
            </div>

            <div className="stat-item">
              <Counter end={1} suffix='.89' />
              <div className="stat-label">
                Projects<br />
                stats here
              </div>
            </div>

            <div className="stat-item">
              <Counter end={0.00} suffix=".00" />
              <div className="stat-label">
                Projects<br />
                stats here
              </div>
            </div>
          </div>

          <div className="seventh-content">
            <h2 className="seventh-title">
            Proven. Trusted. Scaling Fast
            Supported by pioneers
            </h2>
            <p className="seventh-description">
            These are just a few numbers, the real value is in shaping the financial understanding and investing behavior in millions of retail investors in India and Beyond
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SeventhSection;
