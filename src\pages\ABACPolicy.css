.abac-policy-container {
  font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
  color: #333;
  line-height: 1.6;
  background-color: #fff;
}

.abac-policy-header {
  background-color: #fff;
  padding: 3rem 0 1.5rem;
  margin-bottom: 1rem;
  position: relative;
}

.abac-policy-header::before {
  content: "";
  position: absolute;
  top: 20px;
  left: 50%;
  width: 6px;
  height: 6px;
  background-color: #ff0000;
  border-radius: 50%;
  transform: translateX(-50%);
}

.abac-policy-header h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: #212529;
  text-align: center;
}

.last-updated {
  color: #333;
  font-size: 1rem;
  margin-bottom: 2rem;
  text-align: center;
}

.last-updated strong {
  font-weight: 600;
}

.abac-policy-content {
  padding: 0 0 5rem;
  max-width: 900px;
  margin: 0 auto;
}

.policy-section {
  margin-bottom: 2rem;
}

.policy-section h2 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #212529;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 0.5rem;
}

.policy-section h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  margin-top: 1.5rem;
  color: #212529;
}

.policy-section p {
  margin-bottom: 1rem;
  text-align: justify;
  font-size: 0.95rem;
}

.policy-section ul {
  padding-left: 1.5rem;
  margin-bottom: 1.5rem;
}

.policy-section li {
  margin-bottom: 0.5rem;
  text-align: justify;
  font-size: 0.95rem;
}

.contact-info-list {
  list-style-type: none;
  padding-left: 0;
}

.contact-info-list li {
  margin-bottom: 0.75rem;
}

.contact-info-list strong {
  font-weight: 600;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .abac-policy-content {
    padding: 0 2rem 5rem;
  }
}

@media (max-width: 992px) {
  .abac-policy-header h1 {
    font-size: 2.5rem;
  }
  
  .abac-policy-content {
    padding: 0 1.5rem 4rem;
  }
}

@media (max-width: 768px) {
  .abac-policy-header {
    padding: 2rem 0 1.5rem;
  }

  .abac-policy-header h1 {
    font-size: 2rem;
  }

  .abac-policy-content {
    padding: 0 1rem 3rem;
  }

  .policy-section h2 {
    font-size: 1.2rem;
  }

  .policy-section h3 {
    font-size: 1rem;
  }

  .policy-section p,
  .policy-section li {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .abac-policy-header {
    padding: 1.5rem 0 1rem;
  }

  .abac-policy-header h1 {
    font-size: 1.75rem;
  }

  .abac-policy-content {
    padding: 0 0.75rem 2.5rem;
  }

  .policy-section h2 {
    font-size: 1.1rem;
  }

  .policy-section h3 {
    font-size: 0.95rem;
  }

  .policy-section p,
  .policy-section li {
    font-size: 0.85rem;
  }
}
