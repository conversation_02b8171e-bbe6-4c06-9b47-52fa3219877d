.privacy-policy-container {
  font-family: system-ui, -apple-system, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  color: #333;
  line-height: 1.6;
  background-color: #fff;
}

.privacy-policy-header {
  background-color: #fff;
  padding: 3rem 0 1.5rem;
  margin-bottom: 1rem;
  position: relative;
}

.privacy-policy-header::before {
  content: "";
  position: absolute;
  top: 20px;
  left: 50%;
  width: 6px;
  height: 6px;
  background-color: #ff0000;
  border-radius: 50%;
  transform: translateX(-50%);
}

.privacy-policy-header h1 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 0.75rem;
  color: #212529;
  text-align: center;
}

.last-updated {
  color: #333;
  font-size: 1rem;
  margin-bottom: 2rem;
  text-align: center;
}

.last-updated strong {
  font-weight: 600;
}

.privacy-policy-content {
  padding: 0 0 5rem;
  max-width: 900px;
  margin: 0 auto;
}

.intro-text {
  font-size: 1rem;
  margin-bottom: 2rem;
  color: #495057;
  text-align: justify;
}

.policy-section {
  margin-bottom: 2rem;
}

.policy-section h2 {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #212529;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 0.5rem;
}

.policy-section p {
  margin-bottom: 1rem;
  text-align: justify;
  font-size: 0.95rem;
}

.policy-section ul {
  padding-left: 1.5rem;
  margin-bottom: 1.5rem;
}

.policy-section li {
  margin-bottom: 0.5rem;
  text-align: justify;
  font-size: 0.95rem;
}

.contact-info-list {
  list-style-type: none;
  padding-left: 0;
}

.contact-info-list li {
  margin-bottom: 0.75rem;
}

/* Footer styling to match the image */
.privacy-policy-footer {
  background-color: #333;
  color: #fff;
  padding: 2rem 0;
  margin-top: 3rem;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.footer-column {
  flex: 1;
  min-width: 200px;
  margin-bottom: 1.5rem;
}

.footer-heading {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #fff;
}

.footer-list {
  list-style: none;
  padding-left: 0;
}

.footer-list li {
  margin-bottom: 0.5rem;
}

.footer-list a {
  color: #ccc;
  text-decoration: none;
  font-size: 0.9rem;
}

.footer-list a:hover {
  color: #fff;
}

@media (max-width: 768px) {
  .privacy-policy-header {
    padding: 2rem 0 1.5rem;
  }

  .privacy-policy-header h1 {
    font-size: 2rem;
  }

  .privacy-policy-content {
    padding: 0 0 3rem;
  }

  .intro-text {
    font-size: 0.95rem;
  }

  .policy-section h2 {
    font-size: 1.2rem;
  }

  .policy-section p,
  .policy-section li {
    font-size: 0.9rem;
  }
}
