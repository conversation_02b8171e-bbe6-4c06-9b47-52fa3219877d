.solution-section {
  padding: 80px 0;
  background-color: #FFFBF7;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.solution-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 100px;
}

.solution-text {
  flex: 1;
  max-width: 600px;
}

.solution-header {
  margin-bottom: 60px;
}

.features-tag {
  display: inline-block;
  background-color: rgba(255, 107, 0, 0.1);
  color: #FF6B00;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 16px;
}

.section-title {
  font-size: 48px;
  font-weight: 700;
  color: #2D3748;
  line-height: 1.2;
  margin: 0;
  letter-spacing: -0.02em;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 60px 80px;
  margin-top: 40px;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.feature-icon {
  font-size: 40px;
  color: #FF6B00 !important;
  margin-bottom: 20px;
  display: block;
}

.feature-title {
  font-size: 20px;
  font-weight: 600;
  color: #2D3748;
  margin: 0 0 12px 0;
  line-height: 1.3;
}

.feature-description {
  font-size: 16px;
  line-height: 1.5;
  color: #718096;
  margin: 0;
  max-width: 250px;
}

.mobile-showcase {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mobile-image {
  max-width: 100%;
  height: auto;
  max-height: 500px;
  object-fit: contain;
}

/* Responsive styles */
@media (max-width: 1024px) {
  .solution-content {
    gap: 60px;
  }

  .section-title {
    font-size: 40px;
  }
}

@media (max-width: 768px) {
  .solution-section {
    padding: 60px 0;
  }

  .container {
    padding: 0 16px;
  }

  .solution-content {
    flex-direction: column;
    gap: 40px;
  }

  .solution-text {
    max-width: 100%;
  }

  .solution-header {
    margin-bottom: 32px;
    text-align: center;
  }

  .section-title {
    font-size: 32px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .mobile-showcase {
    order: -1;
  }
}

@media (max-width: 480px) {
  .solution-section {
    padding: 40px 0;
  }

  .section-title {
    font-size: 28px;
  }

  .features-grid {
    gap: 24px;
  }

  .feature-icon {
    font-size: 28px;
  }
}
