import React, { useState } from 'react';

const AboutWireframeKitSection: React.FC = () => {
  const [activeCard, setActiveCard] = useState<number>(0); // First card is active by default

  const handleCardHover = (cardIndex: number) => {
    setActiveCard(cardIndex);
  };

  const handleContainerLeave = () => {
    setActiveCard(0); // Reset to first card when leaving the grid
  };

  return (
    <section className="about-wireframe-section">
      <div className="about-wireframe-container">
        <div className="about-wireframe-left">
          <h2 className="about-wireframe-title">Born in Bharat. 
          Built for Modern Investors</h2>
          <p className="about-wireframe-description">
          Arth: isn't just another interface for market access. It's an investing OS — one that learns, adapts, and grows with you. Whether you're just starting or scaling your portfolio, Arth: helps you make smarter, faster, and more emotionally grounded decisions.
          </p>
          <button className="about-wireframe-button">Button</button>
        </div>
        <div className="about-wireframe-right">
          <div className="about-wireframe-grid" onMouseLeave={handleContainerLeave}>
            <div 
              className={`about-wireframe-card ${activeCard === 0 ? 'dark' : 'light'}`}
              onMouseEnter={() => handleCardHover(0)}
            >
              <div className="about-card-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M3 13h4v8H3v-8zm6-5h4v13H9V8zm6-5h4v18h-4V3z" fill="currentColor"/>
                </svg>
              </div>
              <h3 className="about-card-title">Built for Bharat</h3>
              <p className="about-card-description">
              It's a modern investing infrastructure designed from the ground up for Indian investors — their languages, behaviors, and realities.
              </p>
            </div>
            <div 
              className={`about-wireframe-card ${activeCard === 1 ? 'dark' : 'light'}`}
              onMouseEnter={() => handleCardHover(1)}
            >
              <div className="about-card-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M3 13h4v8H3v-8zm6-5h4v13H9V8zm6-5h4v18h-4V3z" fill="currentColor"/>
                </svg>
              </div>
              <h3 className="about-card-title">No Retrofitting</h3>
              <p className="about-card-description">
              We're not retrofitting global models, We have built state of the art fundamental models that solves for real analytical and behavioral aspects of investing
              </p>
            </div>
            <div 
              className={`about-wireframe-card ${activeCard === 2 ? 'dark' : 'light'}`}
              onMouseEnter={() => handleCardHover(2)}
            >
              <div className="about-card-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M3 13h4v8H3v-8zm6-5h4v13H9V8zm6-5h4v18h-4V3z" fill="currentColor"/>
                </svg>
              </div>
              <h3 className="about-card-title">Built Around You</h3>
              <p className="about-card-description">
              We're solving for volatility fears in, liquidity needs for growing families, late-night questions in Hinglish, and a desire to feel understood, not just serviced.
              </p>
            </div>
            <div 
              className={`about-wireframe-card ${activeCard === 3 ? 'dark' : 'light'}`}
              onMouseEnter={() => handleCardHover(3)}
            >
              <div className="about-card-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                  <path d="M3 13h4v8H3v-8zm6-5h4v13H9V8zm6-5h4v18h-4V3z" fill="currentColor"/>
                </svg>
              </div>
              <h3 className="about-card-title">Inclusivity</h3>
              <p className="about-card-description">
              We're analysts, economists, quant engineers, & traders — all united by one vision: to make structured investing feel natural, intuitive, and trustworthy
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutWireframeKitSection;
