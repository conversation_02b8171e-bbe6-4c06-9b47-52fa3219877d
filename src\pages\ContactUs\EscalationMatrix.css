.custom-button {
  background-color: #484848 !important;
  border-color: #484848 !important;
  color: #fff !important;
}

.custom-card {
  position: relative;
  border: 2px solid #ed7d22;
  border-left: 6px solid transparent; /* space for strip */
  padding-left: 12px;
}


.strip-orange::before,
.strip-green::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 6px;
  height: 100%;
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.strip-orange::before {
  background-color: #ed7d22; /* Orange */
}

.strip-green::before {
  background-color: #ed7d22;
}

.link-style {
  color: #484848;
  text-decoration: none;
}

.link-style:hover {
  text-decoration: underline;
  color: #484848; /* Optional: keep the same color on hover */
}

.timeline-list {
  position: relative;
  padding-left: 2em; /* space for the line and dot */
  list-style: none;
}

.timeline-list::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0.95 em; /* aligns with dot center */
  width: 0.125em;
  height: 100%;
  background-color: #c0c0c0;
  z-index: 0;
  opacity: 0.5;
}

.timeline-list li {
  position: relative;
  padding-left: 1.5em;
  margin-bottom: 2.5em;
}

.timeline-dot {
  width: 0.875em;
  height: 0.875em;
  background-color: #505050;
  border: 0.125em solid #505050;
  border-radius: 50%;
  position: absolute;
  left: 0em; /* aligns center of dot with the line at 0.5em */
  top: 1em;
  z-index: 2;
    left: 0.75em;
  transform: translateX(-50%);
}
